import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/share_with_image/share_with_image_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_constants.dart';

// region Buyer Share Screen
class ShareWithImageScreen extends StatefulWidget {
  final String url;
  final String? imageLink;
  final CustomImageContainerType imageType;
  final EntityType entityType;
  final String message;
  final String? storeName;
  final String? productName;
  final String? productBrand;
  final String? storeIconUrl;
  final String? postText;
  final String? postCreatorName;
  final String? postCreatorIcon;
  final String? objectReference;
  final String?
      attachmentImagePath; // Path to local image file to be shared as attachment

  const ShareWithImageScreen({
    Key? key,
    required this.url,
    this.imageLink, // Made nullable - removed required
    required this.imageType,
    required this.entityType,
    this.message = "",
    this.storeName,
    this.productName,
    this.productBrand,
    this.storeIconUrl,
    this.postText,
    this.postCreatorName,
    this.postCreatorIcon,
    this.objectReference,
    this.attachmentImagePath,
  }) : super(key: key);

  @override
  _ShareWithImageScreenState createState() => _ShareWithImageScreenState();
}
// endregion

class _ShareWithImageScreenState extends State<ShareWithImageScreen> {
  // region Bloc
  late ShareWithImageBloc shareWithImageBloc;
  bool isSearchVisible = false;

  // endregion

  // region Init
  @override
  void initState() {
    debugPrint('DEBUG: ShareWithImageScreen initState');
    debugPrint('DEBUG: Received Store Name: ${widget.storeName}');
    debugPrint('DEBUG: Received Product Name: ${widget.productName}');
    debugPrint('DEBUG: Received Brand Name: ${widget.productBrand}');
    debugPrint('DEBUG: Received Store Icon: ${widget.storeIconUrl}');
    debugPrint('DEBUG: Received Post Text: ${widget.postText}');
    debugPrint('DEBUG: Received Post Creator Name: ${widget.postCreatorName}');
    debugPrint('DEBUG: Received Post Creator Icon: ${widget.postCreatorIcon}');

    shareWithImageBloc = ShareWithImageBloc(
      context,
      widget.url,
      widget.message,
      storeName: widget.storeName,
      productName: widget.productName,
      productBrand: widget.productBrand,
      storeIconUrl: widget.storeIconUrl,
      postText: widget.postText,
      postCreatorName: widget.postCreatorName,
      postCreatorIcon: widget.postCreatorIcon,
      imageLink: widget.imageLink,
      attachmentImagePath: widget.attachmentImagePath,
      entityType: widget.entityType,
    );
    shareWithImageBloc.init();
    super.initState();
  }

  // endregion

  // region Dispose
  @override
  void dispose() {
    shareWithImageBloc.dispose();
    super.dispose();
  }

  // endregion

  // region Create Shared Object Reference
  String createSharedObjectReference() {
    // Create a simple object reference based on the entity type
    return widget.objectReference ?? "";
  }
  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: body(),
    );
  }

  // endregion

  // region Body
  Widget body() {
    final ScrollController scrollController = ScrollController();
    return SingleChildScrollView(
      controller: scrollController,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Handle bar for dragging and back button
          Padding(
            padding: const EdgeInsets.only(top: 15, bottom: 25),
            child: Row(
              children: [
                // Back button
                Padding(
                  padding: const EdgeInsets.only(left: 20),
                  child: InkWell(
                    onTap: () => Navigator.of(context).pop(),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.arrow_back,
                        size: 20,
                        color: Colors.black54,
                      ),
                    ),
                  ),
                ),
                // Centered handle bar
                Expanded(
                  child: Center(
                    child: Container(
                      width: 40,
                      height: 5,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ),
                // Spacer to balance the back button
                const SizedBox(width: 48),
              ],
            ),
          ),
          // Show attachment indicator if image is attached
          if (widget.attachmentImagePath != null &&
              widget.attachmentImagePath!.isNotEmpty) ...[
            _buildAttachmentIndicator(),
            verticalSizedBox(20), // Add space after the image
          ],

          // Only show image section if imageLink is not null
          if (widget.imageLink != null) ...[
            if (widget.entityType == EntityType.POST)
              _buildPostPreview()
            else if (widget.entityType == EntityType.USER)
              _buildUserProfilePreview()
            else if (widget.entityType == EntityType.STORE)
              _buildStoreProfilePreview()
            else
              CustomImageContainer(
                width: 180,
                height: 180,
                imageUrl: widget.imageLink,
                imageType: widget.imageType,
              ),
            verticalSizedBox(12),
          ],

          widget.entityType != EntityType.POST &&
                  widget.entityType != EntityType.USER &&
                  widget.entityType != EntityType.STORE
              ? _buildStoreProductInfo()
              : const SizedBox(),

          // verticalSizedBox(12),

          _buildMessageTextField(),

          verticalSizedBox(15),

          // Contacts list header with search button
          _buildChatsListHeader(),

          // Search field for contacts (conditionally shown)
          if (isSearchVisible)
            Padding(
              padding: const EdgeInsets.only(top: 10, bottom: 10),
              child: _buildSearchField(),
            ),

          // Contacts list (horizontal scrollable)
          _buildChatsListContent(),

          verticalSizedBox(15),

          // Stack to position send button over share options
          Stack(
            alignment: Alignment.center,
            children: [
              Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: divider(),
                  ),
                  verticalSizedBox(15),
                  copyShare(),
                  verticalSizedBox(15),
                ],
              ),
              // Send button positioned over the share options
              _buildSendButton(),
            ],
          ),
        ],
      ),
    );
  }
  // endregion

  // region Message Text Field
  Widget _buildMessageTextField() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: SizedBox(
        child: TextFormField(
          textInputAction: TextInputAction.newline,
          maxLength: 200,
          maxLines: 3, // Reduced from 5 to 3 (30% reduction)
          minLines: 3, // Reduced from 5 to 3 (30% reduction)
          controller: shareWithImageBloc.messageFieldCtrl,
          keyboardType: TextInputType.multiline,
          style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0),
          decoration: InputDecoration(
            filled: true,
            fillColor: AppColors.textFieldFill1,
            hintText: AppStrings.writeAMessage,
            hintStyle: AppTextStyle.contentText0(
                textColor: AppColors.writingBlack1.withOpacity(0.5)),
            contentPadding: const EdgeInsets.all(10),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(9),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(9),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(9),
              borderSide: BorderSide.none,
            ),
            counterText: "", // Hide the default counter
            suffixText:
                "${shareWithImageBloc.messageFieldCtrl.text.length}/200", // Add the counter as suffix text
            suffixStyle: AppTextStyle.contentText0(
                textColor: AppColors.writingBlack1.withOpacity(0.5)),
          ),
          onChanged: (text) {
            // Force rebuild to update the counter
            setState(() {});
          },
          buildCounter: (context,
              {required currentLength, required isFocused, maxLength}) {
            return null; // Return null to hide the default counter
          },
        ),
      ),
    );
  }

  // region Attachment Indicator - Simple image preview
  Widget _buildAttachmentIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Align(
        alignment: Alignment.centerLeft,
        child: FutureBuilder<ui.Image>(
          future: _getImageInfo(File(widget.attachmentImagePath!)),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              // Show loading state
              return Container(
                width: 200,
                height: 150,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.grey[100],
                ),
                child: const Center(
                  child: CircularProgressIndicator(
                    color: AppColors.brandBlack,
                    strokeWidth: 2,
                  ),
                ),
              );
            } else if (snapshot.hasError) {
              // Show error state
              return Container(
                width: 200,
                height: 150,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.grey[100],
                ),
                child: const Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 40,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Failed to load image',
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            } else if (snapshot.hasData) {
              final image = snapshot.data!;
              final aspectRatio = image.width / image.height;

              // Calculate dimensions with max width of 250px and scale factor
              const double maxWidth = 250.0;
              const double scaleFactor =
                  0.8; // Adjust this to make image bigger/smaller

              const double displayWidth = maxWidth * scaleFactor;
              final double displayHeight = displayWidth / aspectRatio;

              return Container(
                width: displayWidth,
                height: displayHeight,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.file(
                    File(widget.attachmentImagePath!),
                    width: displayWidth,
                    height: displayHeight,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: displayWidth,
                        height: displayHeight,
                        color: Colors.grey[100],
                        child: const Center(
                          child: Icon(
                            Icons.broken_image,
                            color: Colors.grey,
                            size: 40,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              );
            } else {
              // Fallback state
              return Container(
                width: 200,
                height: 150,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.grey[100],
                ),
                child: const Center(
                  child: Icon(
                    Icons.image_not_supported,
                    color: Colors.grey,
                    size: 40,
                  ),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  // Helper method to get image dimensions
  Future<ui.Image> _getImageInfo(File imageFile) async {
    try {
      // Check if file exists
      if (!await imageFile.exists()) {
        throw Exception('Image file does not exist');
      }

      final Completer<ui.Image> completer = Completer();
      final ImageStream stream =
          FileImage(imageFile).resolve(const ImageConfiguration());

      late ImageStreamListener listener;
      listener = ImageStreamListener(
        (ImageInfo info, bool _) {
          if (!completer.isCompleted) {
            completer.complete(info.image);
          }
          stream.removeListener(listener);
        },
        onError: (exception, stackTrace) {
          if (!completer.isCompleted) {
            completer.completeError(exception, stackTrace);
          }
          stream.removeListener(listener);
        },
      );

      stream.addListener(listener);

      // Add timeout to prevent infinite loading
      return completer.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          stream.removeListener(listener);
          throw Exception('Image loading timeout');
        },
      );
    } catch (e) {
      throw Exception('Failed to load image: $e');
    }
  }
  // endregion

  // region Search Field
  Widget _buildSearchField() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        height: 43,
        decoration: BoxDecoration(
          color: AppColors.textFieldFill1,
          borderRadius: BorderRadius.circular(22),
        ),
        child: Row(
          children: [
            SvgPicture.asset(AppImages.searchBarIcon, fit: BoxFit.contain),
            horizontalSizedBox(8),
            Expanded(
              child: TextFormField(
                textCapitalization: TextCapitalization.sentences,
                keyboardType: TextInputType.text,
                maxLines: 1,
                controller: shareWithImageBloc.searchFieldCtrl,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: AppColors.appBlack,
                ),
                onChanged: (value) {
                  if (value.isNotEmpty) {
                    shareWithImageBloc.searchChats(value);
                  } else {
                    shareWithImageBloc.fetchChats();
                  }
                },
                decoration: const InputDecoration(
                  filled: true,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  fillColor: AppColors.textFieldFill1,
                  isDense: true,
                  hintText: 'Search contacts...',
                  hintStyle: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColors.writingColor3,
                  ),
                  border: InputBorder.none,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  // endregion

  // region Store Info Widget
  Widget _buildStoreProductInfo() {
    return StreamBuilder<Map<String, dynamic>>(
      stream: shareWithImageBloc.storeDetailsCtrl.stream,
      initialData: {
        'storeName': widget.storeName,
        'productName': widget.productName,
        'productBrand': widget.productBrand,
        'storeIconUrl': widget.storeIconUrl,
      },
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data == null) {
          return const SizedBox();
        }

        final data = snapshot.data!;
        final storeName = data['storeName'] as String?;
        final productName = data['productName'] as String?;
        final productBrand = data['productBrand'] as String?;
        final storeIconUrl = data['storeIconUrl'] as String?;

        if (storeName == null && productName == null && productBrand == null) {
          return const SizedBox();
        }

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              CustomImageContainer(
                width: 40,
                height: 40,
                imageUrl: storeIconUrl,
                imageType: CustomImageContainerType.store,
              ),
              horizontalSizedBox(12),
              Expanded(
                child: RichText(
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  text: TextSpan(
                    children: [
                      if (productBrand?.isNotEmpty == true) ...[
                        TextSpan(
                          text: productBrand!,
                          style: AppTextStyle.contentHeading0(
                            textColor: AppColors.writingBlack0,
                          ),
                        ),
                      ],
                      if (productName?.isNotEmpty == true) ...[
                        TextSpan(
                          text: ' $productName',
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.writingBlack1,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  // endregion

  // region Post Preview Widget
  Widget _buildPostPreview() {
    return StreamBuilder<Map<String, dynamic>>(
      stream: shareWithImageBloc.postDetailsCtrl.stream,
      initialData: {
        'postText': widget.postText,
        'postCreatorName': widget.postCreatorName,
        'postCreatorIcon': widget.postCreatorIcon,
      },
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data == null) {
          return const SizedBox();
        }

        final data = snapshot.data!;
        final postText = data['postText'] as String?;
        final postCreatorName = data['postCreatorName'] as String?;
        final postCreatorIcon = data['postCreatorIcon'] as String?;

        return Container(
          width: double.infinity,
          height: 120, // Fixed compact height
          margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left side: Creator info and post text
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Post creator info
                      Row(
                        children: [
                          // Creator icon
                          CustomImageContainer(
                            width: 32,
                            height: 32,
                            imageUrl: postCreatorIcon,
                            imageType: CustomImageContainerType.user,
                          ),
                          horizontalSizedBox(8),
                          // Creator name
                          Expanded(
                            child: Text(
                              postCreatorName ?? 'Unknown',
                              style: AppTextStyle.contentHeading0(
                                textColor: AppColors.writingBlack0,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      verticalSizedBox(8),
                      // Post text
                      if (postText != null && postText.isNotEmpty)
                        Expanded(
                          child: Text(
                            postText,
                            style: AppTextStyle.contentText0(
                              textColor: AppColors.writingBlack0,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              // Right side: Post image
              if (widget.imageLink != null && widget.imageLink!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: CustomImageContainer(
                        width: 100,
                        height: 100,
                        imageUrl: widget.imageLink,
                        imageType: widget.imageType,
                        showShadow: false,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
  // endregion

//region Image And Write Message
  Widget imageMessage() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Product Image - only show if imageLink is not null and not POST entity
          if (widget.entityType != EntityType.POST &&
              widget.imageLink != null) ...[
            CustomImageContainer(
              width: 180,
              height: 180,
              imageUrl: widget.imageLink,
              imageType: widget.imageType,
            ),
            verticalSizedBox(12),
          ],
          // Message TextField
          _buildMessageTextField(),
        ],
      ),
    );
  }
//endregion

//region Copy Share Link
  Widget copyShare() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 50),
      child: SizedBox(
        height: 60,
        child: Opacity(
          opacity:
              0.7, // Slightly fade the share options to make the send button stand out
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              InkWell(
                  onTap: () {
                    shareWithImageBloc.openWhatsapp();
                  },
                  child: SvgPicture.asset(
                    AppImages.whatsappIcon,
                    fit: BoxFit.contain,
                  )),
              horizontalSizedBox(30),
              // InkWell(
              //     onTap: (){
              //       shareWithImageBloc.openFacebook();
              //     },
              //     child: Image.asset(AppImages.facebookIconPng,fit: BoxFit.contain,)),
              InkWell(
                  onTap: () {
                    shareWithImageBloc.shareLinkMessage();
                  },
                  child: SvgPicture.asset(
                    AppImages.externalShareIcon,
                    fit: BoxFit.cover,
                  )),
              horizontalSizedBox(30),
              InkWell(
                  onTap: () {
                    shareWithImageBloc.copyMessageUrl();
                  },
                  child: SvgPicture.asset(
                    AppImages.linkIcon,
                    fit: BoxFit.contain,
                  )),
            ],
          ),
        ),
      ),
    );
  }
//endregion

//region Search Field People and Store
  Widget searchFieldStorePeople() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        height: 43,
        decoration: const BoxDecoration(
            color: AppColors.textFieldFill1,
            borderRadius: BorderRadius.all(Radius.circular(22))),
        child: Row(
          children: [
            SvgPicture.asset(
              AppImages.searchBarIcon,
              fit: BoxFit.contain,
            ),
            Expanded(
              child: TextFormField(
                textCapitalization: TextCapitalization.sentences,
                keyboardType: TextInputType.text,
                maxLines: 1,
                controller: shareWithImageBloc.searchFieldCtrl,
                style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: AppColors.appBlack),
                decoration: InputDecoration(
                  // prefixIcon: SvgPicture.asset(AppImages.emoji,fit:BoxFit.contain,),
                  filled: true,

                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 10),

                  fillColor: AppColors.textFieldFill1,

                  isDense: true,

                  hintText: AppStrings.commentHint,
                  hintStyle: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: AppColors.writingColor3),
                  border: InputBorder.none,
                  // focusedBorder: OutlineInputBorder(
                  //     borderRadius: BorderRadius.circular(22),
                  //     borderSide: BorderSide.none
                  //
                  // ),
                  // enabledBorder: OutlineInputBorder(
                  //
                  //     borderRadius: BorderRadius.circular(22),
                  //     borderSide: BorderSide.none
                  // ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
//endregion

//region Search Result
// Widget searchResult(){
//     return Padding(
//       padding: EdgeInsets.symmetric(horizontal: 20),
//       child: ListView.builder(
//         padding: EdgeInsets.zero,
//           physics: const NeverScrollableScrollPhysics(),
//           itemCount:BuyerHomeBloc.userDetailsResponse.followers!.length,
//           shrinkWrap: true,
//           itemBuilder:(context,index){
//           var user = BuyerHomeBloc.userDetailsResponse.followers!;
//         return Padding(
//           padding:const EdgeInsets.symmetric(vertical: 10),
//           child: SizedBox(
//             height: 36,
//             child: Row(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Container(
//                   width: 36,
//                   decoration:  const BoxDecoration(
//                     color: AppColors.lightGray,
//                     shape: BoxShape.circle,
//                   ),
//                   child: user[index].icon==null?const Icon(Icons.person,size: 30,):extendedImage(user[index].icon!, context, 50, 50),
//                 ),
//                 horizontalSizedBox(15),
//                 Expanded(
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Text(user[index].userName!,style: TextStyle(
//                         fontSize: 14,
//                         fontFamily: "LatoRegular",
//                         fontWeight: FontWeight.w400
//                       ),),
//                       verticalSizedBox(1),
//                       Text("@${user[index].userReference!}",style: TextStyle(
//                           fontSize: 12,
//                           fontFamily: "LatoSemiBold",
//                           fontWeight: FontWeight.w400
//                       ),)
//                     ],
//                   ),
//                 ),
//
//                 Container(
//                   height: double.infinity,
//                   padding: EdgeInsets.symmetric(horizontal: 10),
//
//                   decoration: BoxDecoration(
//                       color: AppColors.brandGreen,
//                     borderRadius: BorderRadius.all(Radius.circular(9))
//                   ),
//                   child:Center(
//                     child: Text("Send",style: TextStyle(
//                         fontSize: 14,
//                         fontFamily: "LatoRegular",
//                         fontWeight: FontWeight.w700,
//                       color: AppColors.appWhite
//                     )),
//                   ),
//                 )
//               ],
//             ),
//           ),
//         );
//       }),
//     );
// }
//endregion

  // region Chats List Header Widget
  Widget _buildChatsListHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Share via message',
            style: AppTextStyle.contentHeading0(
              textColor: AppColors.writingBlack0,
            ),
          ),
          // Search button
          InkWell(
            onTap: () {
              setState(() {
                isSearchVisible = !isSearchVisible;
                if (!isSearchVisible) {
                  // Clear search when hiding
                  shareWithImageBloc.searchFieldCtrl.clear();
                  shareWithImageBloc.fetchChats();
                }
              });
            },
            child: Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color:
                    isSearchVisible ? AppColors.brandBlack : Colors.transparent,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.search,
                size: 20,
                color: isSearchVisible ? Colors.white : AppColors.writingBlack0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // region Chats List Content Widget
  Widget _buildChatsListContent() {
    return Container(
      height: 250, // Increased height for grid with 3 rows
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: StreamBuilder<List<Map<String, dynamic>>>(
        stream: shareWithImageBloc.chatsCtrl.stream,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (!snapshot.hasData ||
              snapshot.data == null ||
              snapshot.data!.isEmpty) {
            return Center(
              child: Text(
                'No chats found',
                style: AppTextStyle.contentText0(
                  textColor: AppColors.writingBlack1,
                ),
              ),
            );
          }

          final chats = snapshot.data!;

          return StreamBuilder<Map<String, dynamic>>(
            stream: shareWithImageBloc.selectedChatCtrl.stream,
            builder: (context, selectedChatSnapshot) {
              // Get the selected chat from the stream or bloc
              Map<String, dynamic>? selectedChat;

              if (selectedChatSnapshot.hasData &&
                  selectedChatSnapshot.data != null &&
                  selectedChatSnapshot.data!.isNotEmpty) {
                selectedChat = selectedChatSnapshot.data!;
              } else {
                selectedChat = shareWithImageBloc.selectedChat;
              }

              // Calculate grid dimensions
              const int crossAxisCount = 4; // Number of columns
              const int maxRows = 3; // Maximum number of rows to display
              const int visibleItems =
                  crossAxisCount * maxRows; // Maximum number of visible items

              return GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  childAspectRatio: 0.8, // Width to height ratio
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                ),
                itemCount:
                    chats.length > visibleItems ? visibleItems : chats.length,
                itemBuilder: (context, index) {
                  final chat = chats[index];
                  // Get the identifiers for this chat/contact
                  final String? chatId = chat['chat_id'];
                  final String? connectingId = chat['connecting_id'];
                  final chatName = chat['chat_name'] ?? 'Unknown';
                  final chatIcon =
                      "${AppConstants.baseMediaUrl}${chat['chat_icon']}";

                  // Check if this chat/contact is selected by comparing both chat_id and connecting_id
                  bool isSelected = false;
                  if (selectedChat != null) {
                    final String? selectedChatId = selectedChat['chat_id'];
                    final String? selectedConnectingId =
                        selectedChat['connecting_id'];

                    // Compare both IDs to determine if it's the same chat
                    if (chatId == selectedChatId &&
                        connectingId == selectedConnectingId) {
                      isSelected = true;
                    }
                  }

                  return GestureDetector(
                    onTap: () {
                      shareWithImageBloc.selectChat(chat);
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Contact avatar with selection indicator
                        Expanded(
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: isSelected
                                        ? AppColors.brandBlack
                                        : Colors.transparent,
                                    width: isSelected ? 2 : 0,
                                  ),
                                ),
                                child: Padding(
                                  padding: EdgeInsets.all(isSelected ? 2 : 0),
                                  child: CircleAvatar(
                                    radius: 28,
                                    backgroundColor: Colors.grey[300],
                                    backgroundImage: chatIcon.isNotEmpty
                                        ? NetworkImage(
                                            "${AppConstants.baseUrl}/$chatIcon")
                                        : null,
                                    child: chatIcon.isEmpty
                                        ? Icon(Icons.person,
                                            size: 28, color: Colors.grey[600])
                                        : null,
                                  ),
                                ),
                              ),
                              if (isSelected)
                                Positioned(
                                  right: 0,
                                  bottom: 0,
                                  child: Container(
                                    width: 20,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      color: AppColors.brandBlack,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                          color: Colors.white, width: 2),
                                    ),
                                    child: const Icon(
                                      Icons.check,
                                      color: Colors.white,
                                      size: 12,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        // Contact name
                        Text(
                          chatName,
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.writingBlack0,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  // region Send Button Widget
  Widget _buildSendButton() {
    return StreamBuilder<bool>(
      stream: shareWithImageBloc.sendingCtrl.stream,
      initialData: false,
      builder: (context, snapshot) {
        final isSending = snapshot.data ?? false;

        return StreamBuilder<Map<String, dynamic>>(
          stream: shareWithImageBloc.selectedChatCtrl.stream,
          builder: (context, chatSnapshot) {
            // Check if we have data and it's not empty
            final hasSelectedChat = chatSnapshot.hasData &&
                chatSnapshot.data != null &&
                chatSnapshot.data!.isNotEmpty;

            // Only show the button when a chat is selected
            if (!hasSelectedChat) {
              return const SizedBox(); // Return empty widget when no chat is selected
            }

            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              width:
                  200, // Fixed width for better appearance over share options
              child: ElevatedButton(
                onPressed: !isSending
                    ? () => shareWithImageBloc
                        .sendMessageToChat(createSharedObjectReference())
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.brandBlack,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(25), // More rounded corners
                  ),
                  elevation:
                      4, // Add shadow for better visibility over other elements
                ),
                child: isSending
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.send, size: 18),
                          horizontalSizedBox(8),
                          Text(
                            'Send Message',
                            style: AppTextStyle.contentHeading0(
                              textColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
              ),
            );
          },
        );
      },
    );
  }

  // region User Profile Preview Widget
  Widget _buildUserProfilePreview() {
    return StreamBuilder<Map<String, dynamic>>(
      stream: shareWithImageBloc.postDetailsCtrl.stream,
      initialData: {
        'postText': widget.postText,
        'postCreatorName': widget.postCreatorName,
        'postCreatorIcon': widget.postCreatorIcon,
      },
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data == null) {
          return const SizedBox();
        }

        final data = snapshot.data!;
        final userName = data['postCreatorName']; // Extract handle from URL
        final displayName = data['postText'] as String?;
        final userIcon = data['postCreatorIcon'] as String?;

        return Container(
          width: double.infinity,
          height: 80, // Smaller height for user profile
          margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              children: [
                // User profile picture
                CustomImageContainer(
                  width: 56,
                  height: 56,
                  imageUrl: userIcon,
                  imageType: CustomImageContainerType.user,
                ),
                horizontalSizedBox(12),
                // User name and handle
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Username/handle
                      Text(
                        userName,
                        style: AppTextStyle.contentHeading0(
                          textColor: AppColors.writingBlack0,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      // Display name
                      if (displayName != null && displayName.isNotEmpty)
                        Text(
                          displayName,
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.writingBlack1,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  // endregion

  // region User Profile Preview Widget
  Widget _buildStoreProfilePreview() {
    return StreamBuilder<Map<String, dynamic>>(
      stream: shareWithImageBloc.storeDetailsCtrl.stream,
      initialData: {
        'storeName': widget.storeName,
        'storeIconUrl': widget.storeIconUrl,
      },
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data == null) {
          return const SizedBox();
        }

        final data = snapshot.data!;
        final storeName = data['storeName'] as String?;
        final storeIconUrl = data['storeIconUrl'] as String?;

        if (storeName == null && storeIconUrl == null) {
          return const SizedBox();
        }

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              CustomImageContainer(
                width: 40,
                height: 40,
                imageUrl: storeIconUrl,
                imageType: CustomImageContainerType.store,
              ),
              horizontalSizedBox(12),
              Expanded(
                child: RichText(
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  text: TextSpan(
                    children: [
                      if (storeName?.isNotEmpty == true) ...[
                        TextSpan(
                          text: storeName!,
                          style: AppTextStyle.contentHeading0(
                            textColor: AppColors.writingBlack0,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  // endregion
}
