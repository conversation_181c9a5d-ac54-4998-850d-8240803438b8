import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/external_reviews/create_external_review_request_bloc.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_constants.dart';

class CreateExternalReviewRequestScreen extends StatefulWidget {
  final Product product;
  final String? productImage;

  const CreateExternalReviewRequestScreen({
    Key? key,
    required this.product,
    this.productImage,
  }) : super(key: key);

  @override
  _CreateExternalReviewRequestScreenState createState() =>
      _CreateExternalReviewRequestScreenState();
}

class _CreateExternalReviewRequestScreenState
    extends State<CreateExternalReviewRequestScreen> {
  late CreateExternalReviewRequestBloc bloc;

  @override
  void initState() {
    super.initState();
    bloc = CreateExternalReviewRequestBloc(
        context, widget.product, widget.productImage);
    bloc.init();
  }

  @override
  void dispose() {
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.appWhite,
        elevation: 0,
        leading: IconButton(
          icon: SvgPicture.asset(AppImages.backButton),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Create External Review Request Link',
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.more_vert, color: AppColors.appBlack),
            onPressed: () {},
          ),
        ],
      ),
      body: StreamBuilder<ExternalReviewRequestState>(
        stream: bloc.stateCtrl.stream,
        initialData: ExternalReviewRequestState.initial,
        builder: (context, snapshot) {
          if (snapshot.data == ExternalReviewRequestState.loading) {
            return Center(child: AppCommonWidgets.appCircularProgress());
          }
          return _buildBody();
        },
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoSection(),
            const SizedBox(height: 24),
            _buildProductPreview(),
            const SizedBox(height: 24),
            _buildBuyerIdentificationSection(),
            const SizedBox(height: 32),
            _buildCreateLinkButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      padding: const EdgeInsets.all(0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bring Trust into your Swadesic store by requesting reviews from your buyers who purchased outside Swadesic.',
            style:
                AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: bloc.onTapKnowMore,
            child: Text(
              'Know more about External Reviews',
              style: AppTextStyle.smallTextRegular(
                  textColor: AppColors.brandBlack, isUnderline: true),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductPreview() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Image
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
            child: widget.productImage != null
                ? Image.network(
                    "${AppConstants.baseUrl}${AppConstants.baseMediaUrlWithslashes}${widget.productImage!}",
                    width: double.infinity,
                    height: 200,
                    fit: BoxFit.cover,
                  )
                : Container(
                    width: double.infinity,
                    height: 200,
                    color: AppColors.lightGray,
                    child: const Icon(
                      Icons.image,
                      color: AppColors.writingBlack1,
                      size: 48,
                    ),
                  ),
          ),
          // Product Details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Store Info
                Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundImage: widget.product.storeIcon != null
                          ? NetworkImage(
                              "${AppConstants.baseUrl}${AppConstants.baseMediaUrlWithslashes}${widget.product.storeIcon!}")
                          : null,
                      child: widget.product.storeIcon == null
                          ? const Icon(Icons.store, size: 16)
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        widget.product.storeName ?? 'Store',
                        style: AppTextStyle.heading1Medium(
                          textColor: AppColors.appBlack,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Product Name
                Text(
                  widget.product.productName ?? 'Product',
                  style: AppTextStyle.heading1Medium(
                    textColor: AppColors.appBlack,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (widget.product.brandName != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    widget.product.brandName!,
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingBlack1),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBuyerIdentificationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Buyer Identification (Optional)',
          style: AppTextStyle.heading1Medium(
            textColor: AppColors.appBlack,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Add a layer of security on who can add an external review by requesting them to identify',
          style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
        ),
        const SizedBox(height: 16),
        _buildIdentifierDropdown(),
        const SizedBox(height: 16),
        _buildIdentifierTextField(),
      ],
    );
  }

  Widget _buildIdentifierDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.lightGray),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          value: bloc.selectedIdentifierType,
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                bloc.selectedIdentifierType = newValue;
              });
            }
          },
          items: <String>['username', 'phone', 'email']
              .map<DropdownMenuItem<String>>((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value.substring(0, 1).toUpperCase() + value.substring(1),
                style: AppTextStyle.smallText(textColor: AppColors.appBlack),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildIdentifierTextField() {
    String hintText = 'Enter the username';
    TextInputType keyboardType = TextInputType.text;

    if (bloc.selectedIdentifierType == 'phone') {
      hintText = 'Enter the phone number';
      keyboardType = TextInputType.phone;
    } else if (bloc.selectedIdentifierType == 'email') {
      hintText = 'Enter the email address';
      keyboardType = TextInputType.emailAddress;
    }

    return TextField(
      controller: bloc.userIdentifierController,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.lightGray),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      style: AppTextStyle.smallText(textColor: AppColors.appBlack),
      keyboardType: keyboardType,
    );
  }

  Widget _buildCreateLinkButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: bloc.onTapCreateLink,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandBlack,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          'Create External Review Request Link',
          style: AppTextStyle.button1Bold(
            textColor: AppColors.appWhite,
          ),
        ),
      ),
    );
  }
}
