import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/features/external_reviews/create_external_review_request_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';

class CreateExternalReviewRequestScreen extends StatefulWidget {
  final Product product;
  final String? productImage;

  const CreateExternalReviewRequestScreen({
    Key? key,
    required this.product,
    this.productImage,
  }) : super(key: key);

  @override
  _CreateExternalReviewRequestScreenState createState() =>
      _CreateExternalReviewRequestScreenState();
}

class _CreateExternalReviewRequestScreenState
    extends State<CreateExternalReviewRequestScreen> {
  late CreateExternalReviewRequestBloc bloc;

  @override
  void initState() {
    super.initState();
    bloc = CreateExternalReviewRequestBloc(
        context, widget.product, widget.productImage);
    bloc.init();
  }

  @override
  void dispose() {
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppCommonWidgets.mainAppBar(
        context: context,
        title: 'Create External Review Request Link',
        isCustomTitle: true,
        customTitleWidget: Text(
          'Create External Review Request Link',
          style: AppTextStyle.pageHeading2(textColor: AppColors.appBlack),
        ),
        isDefaultMenuVisible: true,
        onTapReport: () {},
        backgroundColor: AppColors.appWhite,
        isMembershipVisible: false,
        isCartVisible: false,
      ),
      body: StreamBuilder<ExternalReviewRequestState>(
        stream: bloc.stateCtrl.stream,
        initialData: ExternalReviewRequestState.initial,
        builder: (context, snapshot) {
          if (snapshot.data == ExternalReviewRequestState.loading) {
            return Center(child: AppCommonWidgets.appCircularProgress());
          }
          return _buildBody();
        },
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoSection(),
            const SizedBox(height: 24),
            _buildProductPreview(),
            const SizedBox(height: 24),
            _buildBuyerIdentificationSection(),
            const SizedBox(height: 32),
            _buildCreateLinkButton(),
            const SizedBox(height: 16),
            _buildFooterText(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      padding: const EdgeInsets.all(0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bring Trust into your Swadesic store by requesting reviews from your buyers who purchased outside Swadesic.',
            style:
                AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: bloc.onTapKnowMore,
            child: Text(
              'Know more about External Reviews',
              style: AppTextStyle.smallTextRegular(
                  textColor: AppColors.brandBlack, isUnderline: true),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductPreview() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Image with rounded corners
          Container(
            width: double.infinity,
            height: 200,
            decoration: const BoxDecoration(
              color: AppColors.appWhite,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: widget.productImage != null
                ? SizedBox(
                    width: double.infinity,
                    height: 200,
                    child: Center(
                      child: PostAndProductImageWidgets(
                        localOrNetworkImage: widget.productImage!,
                        imageSize: 167, // Scale down the image to 70%
                      ),
                    ),
                  )
                : Container(
                    width: double.infinity,
                    height: 200,
                    color: AppColors.lightGray,
                    child: const Icon(
                      Icons.image,
                      color: AppColors.writingBlack1,
                      size: 48,
                    ),
                  ),
          ),
          // Product Details
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            // padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Store Icon using CustomImageContainer
                CustomImageContainer(
                  width: 45,
                  height: 45,
                  imageUrl: widget.product.storeIcon != null &&
                          widget.product.storeIcon!.isNotEmpty
                      ? widget.product.storeIcon!
                      : null,
                  imageType: CustomImageContainerType.store,
                ),
                const SizedBox(width: 12),
                // Brand name + Product name with smart concatenation
                Expanded(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final productName =
                          widget.product.productName ?? 'Product';
                      final brandName = widget.product.brandName;

                      // Create a TextPainter to measure the product name
                      final textPainter = TextPainter(
                        text: TextSpan(
                          text: productName,
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                        maxLines: 1,
                        textDirection: TextDirection.ltr,
                      )..layout(maxWidth: constraints.maxWidth);

                      final isProductNameSingleLine =
                          !textPainter.didExceedMaxLines;

                      if (brandName == null || brandName.isEmpty) {
                        return Text(
                          productName,
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        );
                      }

                      if (isProductNameSingleLine) {
                        // Brand on top, product name below
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              brandName,
                              style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack,
                              ).copyWith(fontWeight: FontWeight.w600),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              productName,
                              style: AppTextStyle.contentText0(
                                textColor: AppColors.appBlack,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        );
                      } else {
                        // Concatenate brand and product name
                        return RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: '$brandName ',
                                style: AppTextStyle.contentHeading0(
                                  textColor: AppColors.appBlack,
                                ).copyWith(fontWeight: FontWeight.w600),
                              ),
                              TextSpan(
                                text: productName,
                                style: AppTextStyle.contentText0(
                                  textColor: AppColors.appBlack,
                                ),
                              ),
                            ],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildBuyerIdentificationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Reviewer Identification (Optional)',
          style: AppTextStyle.contentHeading0(
            textColor: AppColors.appBlack,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Add a layer of security on who can add an external review by requesting them to identify',
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)
              .copyWith(
            fontSize: 13,
          ),
        ),
        const SizedBox(height: 16),
        _buildIdentifierDropdown(),
        const SizedBox(height: 16),
        _buildIdentifierTextField(),
      ],
    );
  }

  Widget _buildIdentifierDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          value: bloc.selectedIdentifierType,
          icon: SvgPicture.asset(
            AppImages.arrow,
            color: AppColors.appBlack,
            height: 24,
            width: 24,
          ),
          underline: const SizedBox(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                bloc.selectedIdentifierType = newValue;
              });
            }
          },
          items: <String>['username', 'phone', 'email']
              .map<DropdownMenuItem<String>>((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value.substring(0, 1).toUpperCase() + value.substring(1),
                style:
                    AppTextStyle.hintText(textColor: AppColors.writingBlack1),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildIdentifierTextField() {
    String hintText = 'Enter the username';
    TextInputType keyboardType = TextInputType.text;

    if (bloc.selectedIdentifierType == 'phone') {
      hintText = 'Enter the phone number';
      keyboardType = TextInputType.phone;
    } else if (bloc.selectedIdentifierType == 'email') {
      hintText = 'Enter the email address';
      keyboardType = TextInputType.emailAddress;
    }

    return AppTextFields.allTextField(
      context: context,
      textEditingController: bloc.userIdentifierController,
      hintText: hintText,
      keyboardType: keyboardType,
      textInputAction: TextInputAction.done,
      enabled: true,
      maxEntry: 100,
      onChanged: (value) {},
      onSaved: () {},
    );
  }

  Widget _buildCreateLinkButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: bloc.onTapCreateLink,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandBlack,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          'Create External Review Request Link',
          style: AppTextStyle.button1Bold(
            textColor: AppColors.appWhite,
          ),
        ),
      ),
    );
  }

  Widget _buildFooterText() {
    return Text(
      'External reviews comes with an expiry, make sure you inform the buyer to add promptly.',
      style: AppTextStyle.smallText(textColor: AppColors.appBlack),
      textAlign: TextAlign.center,
    );
  }
}
