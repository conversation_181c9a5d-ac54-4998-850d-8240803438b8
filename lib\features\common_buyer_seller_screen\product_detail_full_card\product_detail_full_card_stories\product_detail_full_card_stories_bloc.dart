import 'dart:async';

import 'package:flutter/cupertino.dart';

class ProductDetailFullCardStoriesBloc {

  //region Common variable
  BuildContext context;
  //endregion



  //region Controller
  final sliderCtrl = StreamController<int>.broadcast();
  //endregion

//region Constructor
  ProductDetailFullCardStoriesBloc(this.context);
//endregion


//region Init
  void init(){
  }
//endregion


//region Dispose
  void dispose(){
    sliderCtrl.close();

  }
//endregion



}