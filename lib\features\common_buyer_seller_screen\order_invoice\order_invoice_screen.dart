import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:pdf/pdf.dart';
import 'package:share_plus/share_plus.dart';
import 'package:swadesic/features/common_buyer_seller_screen/order_invoice/order_invoice_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:webview_flutter/webview_flutter.dart';

class OrderInvoiceScreen extends StatefulWidget {
  final String orderNumber;
  const OrderInvoiceScreen({super.key, required this.orderNumber});

  @override
  State<OrderInvoiceScreen> createState() => _OrderInvoiceScreenState();
}

class _OrderInvoiceScreenState extends State<OrderInvoiceScreen> {
  //region Bloc
  late OrderInvoiceBloc orderInvoiceBloc;
  //endregion

  //region Init
  @override
  void initState() {
    orderInvoiceBloc = OrderInvoiceBloc(context, widget.orderNumber);
    orderInvoiceBloc.init();
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        floatingActionButton: downloadButton(), appBar: appBar(), body: body());
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: "${AppStrings.invoice} (${widget.orderNumber})",
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

//endregion

  //region Body
  Widget body() {
    return StreamBuilder<OrderInvoiceState>(
        stream: orderInvoiceBloc.buyerInvoiceCtrl.stream,
        initialData: OrderInvoiceState.Loading,
        builder: (context, snapshot) {
          //Success
          if (snapshot.data == OrderInvoiceState.Success) {
            return PDFView(
              filePath: orderInvoiceBloc.pdfPath!.path,
              enableSwipe: true,
              swipeHorizontal: true,
              autoSpacing: false,
              pageFling: false,
            );
          }
          //Loading
          if (snapshot.data == OrderInvoiceState.Loading) {
            return Center(child: AppCommonWidgets.appCircularProgress());
          }
          //Failed
          return AppCommonWidgets.errorWidget(
              errorMessage: "Failed to load invoice",
              onTap: () {
                orderInvoiceBloc.createPdfFromHtml();
              });
        });
  }
//endregion

  Widget htmlWebView() {
    return WebView(
      initialUrl: Uri.dataFromString('''
         <!DOCTYPE html>
<html>
<head>
    <title>Ram ram</title>
    <style>
        .print-button {
            margin-top: 20px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
        }

        @media print {
            .print-button {
                display: none;
            }
        }
    </style>
    <script>
        function printPage() {
            // Adding a slight delay before printing
            setTimeout(function() {
                window.print();
            }, 100);  // 100 milliseconds delay
        }
    </script>
</head>
<body>
    <p class="lw">Hello Weaver hjhjh!</p>
    <button class="print-button" onclick="printPage()">Print this page</button>
</body>
</html>

        ''', mimeType: 'text/html').toString(),
      onWebViewCreated: (WebViewController webViewController) {
        orderInvoiceBloc.webViewController = webViewController;
      },
      javascriptMode: JavascriptMode
          .unrestricted, // Enable unrestricted JavaScript execution

      onPageFinished: (String url) {
        // You can optionally call JavaScript here if needed
      },
    );
  }

//region Floating Download button

  Widget downloadButton() {
    return StreamBuilder<OrderInvoiceState>(
        stream: orderInvoiceBloc.buyerInvoiceCtrl.stream,
        initialData: OrderInvoiceState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == OrderInvoiceState.Success) {
            return FloatingActionButton(
              onPressed: () {
                orderInvoiceBloc.downloadInvoiceToDownloadFolder();
              },
              backgroundColor: AppColors.brandBlack,
              child: const Icon(Icons.download),
            );
          }

          return const SizedBox();
        });
  }

//endregion
}
