
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/order_history/product_order_history_screen/product_order_history_screen.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

enum OrderHistoryState { Loading, Success, Failed, Empty }

class OrderHistoryBloc {
  // region Common Variables
  BuildContext context;

  // endregion

  //region Controller
  final orderHistoryCtrl = StreamController<OrderHistoryState>.broadcast();

  //endregion

  //region Text Controller
  //endregion

  // region | Constructor |
  OrderHistoryBloc(this.context);
  // endregion

  // region Init
  void init() {
    orderHistoryCtrl.close();
  }
// endregion





//region Go to product order
  goToProductOrder({ required SubOrder subOrder}){
    var screen =  ProductOrderHistoryScreen(subOrder: subOrder,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion


  // region Dispose
  void dispose() {

  }
// endregion



}
