import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class InitialStoreAndPeopleCard extends StatefulWidget {
  final bool isStore;
  final UserAndStoreInfo recommendedStoreAndUser;
  final Function onTapFollowSupport;
  final Function onTapIcon;
  final CustomImageContainerType customImageContainerType;

  const InitialStoreAndPeopleCard(
      {super.key, required this.isStore, required this.recommendedStoreAndUser, required this.onTapFollowSupport, required this.onTapIcon, required this.customImageContainerType});

  @override
  State<InitialStoreAndPeopleCard> createState() => _InitialStoreAndPeopleCardState();
}

class _InitialStoreAndPeopleCardState extends State<InitialStoreAndPeopleCard> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return InkWell(
      onTap: () {
        widget.onTapIcon();
      },
      child: Container(
        margin: const EdgeInsets.only(top: 5, bottom: 5, left: 10, right: 10),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            //Image
            Container(
              margin: const EdgeInsets.only(right: 10),
              child: CustomImageContainer(
                width: 46,
                height: 46,
                imageUrl: widget.recommendedStoreAndUser.icon,
                imageType: widget.customImageContainerType,
              ),
              // child: ClipRRect(
              //   borderRadius: BorderRadius.circular(imageRadius),
              //   child: SizedBox(
              //     height: 46,
              //     width: 46,
              //     child: extendedImage(
              //         widget.recommendedStoreAndUser.icon, customPlaceHolder: widget.isStore?AppImages.storePlaceHolder:AppImages.userPlaceHolder, context, 200, 200,fit: BoxFit.cover),
              //   ),
              // ),
            ),
            //Name and number
            //Put the phone number if handle is null
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ///Handle heading
                  Visibility(
                     visible: widget.recommendedStoreAndUser.handle!.isNotEmpty,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          "${widget.recommendedStoreAndUser.handle}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack0).copyWith(height: 0),
                        ),
                        VerifiedBadge(
                          width: 15,
                          height: 15,
                          subscriptionType: widget.recommendedStoreAndUser.subscriptionType,
                        ),
                      ],
                    ),
                  ),

                  ///Store title
                  Visibility(
                    visible: widget.isStore,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Flexible(
                          child: Text(
                            "${widget.recommendedStoreAndUser.name}",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0,isLineHeightEnable: false),
                          ),
                        ),
                        Visibility(
                            visible:widget.recommendedStoreAndUser.followersOrSupportersCount! > 0 ,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.end,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(" • ",style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0,isLineHeightEnable: false)),
                                // SvgPicture.asset(AppImages.userIcon,height: 16,width: 16,color: AppColors.appBlack,),
                                Text("${CommonMethods.singularPluralText(item: widget.recommendedStoreAndUser.followersOrSupportersCount!, singular:"Supporter", plural: "Supporters")}",
                                  style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0,isLineHeightEnable: false),
                                ),
                              ],
                            ),

                        )

                      ],
                    ),
                  ),

                  ///User title
                  Visibility(
                    visible: !widget.isStore,
                    child:Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Flexible(
                          child: Text(
                            "${widget.recommendedStoreAndUser.name}",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0,isLineHeightEnable: false),
                          ),
                        ),
                        Visibility(
                          visible:widget.recommendedStoreAndUser.followersOrSupportersCount! > 0 ,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(" • ",style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0)),
                              // SvgPicture.asset(AppImages.userIcon,height: 16,width: 16,color: AppColors.appBlack,),
                              Text("${CommonMethods.singularPluralText(item: widget.recommendedStoreAndUser.followersOrSupportersCount!, singular:"Follower", plural: "Followers")}",
                                style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0,isLineHeightEnable: false).copyWith(),
                              ),
                            ],
                          ),

                        )

                      ],
                    ),
                  ),

                ],
              ),
            ),
            const SizedBox(width: 20,),
            //Button
            actionButton()
            // Visibility(
            //   //Hide this if owner is seeing it's own profile
            //   visible: widget.recommendedStoreAndUser.reference != (AppConstants.appData.isUserView! ? AppConstants.appData.userReference! : AppConstants.appData.storeReference!),
            //   child: SizedBox(
            //     // width: constraints.maxWidth * 0.4,
            //     width: CommonMethods.textWidth(
            //           context: AppConstants.globalNavigator.currentContext!,
            //           text: "Support back",
            //           textStyle: AppTextStyle.access0(textColor: AppColors.appWhite),
            //         ) +
            //         15,
            //     child: Opacity(
            //       opacity: widget.recommendedStoreAndUser.followStatus!.contains("Supporting") ||
            //           widget.recommendedStoreAndUser.followStatus!.contains("Following") ? 0.2 : 1.0,
            //       child: CupertinoButton(
            //         padding: EdgeInsets.zero,
            //         onPressed: () {
            //           //print(CommonMethods.textWidth(
            //           //  context: context,
            //           //  textStyle: AppTextStyle.access0(textColor: AppColors.appBlack),
            //           //  text: "Support back"
            //           //));
            //           widget.onTapFollowSupport();
            //         },
            //         // onPressed: snapshot.data != FindYourCustomerCardState.Loading?(){
            //         //   findYourCustomerCardBloc.onTapAction(contactUserAndStoreInfo:findYourCustomerCardBloc.contactUserAndStoreInfo );
            //         // }:null,
            //         child: Container(
            //           alignment: Alignment.center,
            //           decoration: BoxDecoration(
            //             border: Border.all(
            //               color: AppColors.appBlack, // Replace with your desired border color
            //               width: 1, // Set the width of the border
            //             ),
            //             borderRadius: BorderRadius.circular(100),
            //           ),
            //           padding: const EdgeInsets.symmetric(vertical: 7),
            //           child: Text(widget.recommendedStoreAndUser.followStatus!,
            //           // child: Text("Support back",
            //               overflow: TextOverflow.ellipsis,
            //               style: AppTextStyle.access0(
            //                 textColor: AppColors.appBlack,
            //               )),
            //         ),
            //       ),
            //     ),
            //   ),
            // )
          ],
        ),
      ),
    );
  }
//endregion


//region Action button
Widget actionButton(){
    return Visibility(
      //Hide this if owner is seeing it's own profile
      visible: widget.recommendedStoreAndUser.reference != (AppConstants.appData.isUserView! ? AppConstants.appData.userReference! : AppConstants.appData.storeReference!),
      child: SizedBox(
        // width: constraints.maxWidth * 0.4,
        width: CommonMethods.textWidth(
          context: AppConstants.globalNavigator.currentContext!,
          text: "Support back",
          textStyle: AppTextStyle.access0(textColor: AppColors.appWhite),
        ) +
            15,
        child: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () {
            //print(CommonMethods.textWidth(
            //  context: context,
            //  textStyle: AppTextStyle.access0(textColor: AppColors.appBlack),
            //  text: "Support back"
            //));
            widget.onTapFollowSupport();
          },
          // onPressed: snapshot.data != FindYourCustomerCardState.Loading?(){
          //   findYourCustomerCardBloc.onTapAction(contactUserAndStoreInfo:findYourCustomerCardBloc.contactUserAndStoreInfo );
          // }:null,
          child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border.all(
                color: widget.recommendedStoreAndUser.followStatus!.contains("Supporting") ||
                    widget.recommendedStoreAndUser.followStatus!.contains("Following") ?AppColors.borderColor1:AppColors.borderColor2, // Replace with your desired border color
                width: 1, // Set the width of the border
              ),
              borderRadius: BorderRadius.circular(100),
            ),
            padding: const EdgeInsets.symmetric(vertical: 7),
            child: Text(widget.recommendedStoreAndUser.followStatus!,
                // child: Text("Support back",
                overflow: TextOverflow.ellipsis,
                style: AppTextStyle.access0(
                  textColor:
                  widget.recommendedStoreAndUser.followStatus!.contains("Supporting") ||
                      widget.recommendedStoreAndUser.followStatus!.contains("Following")?
                  AppColors.writingBlack1:AppColors.writingBlack0,
                )),
          ),
        ),
      ),
    );
}
//endregion


}
