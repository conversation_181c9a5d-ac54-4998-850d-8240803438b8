import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_image/product_detail_full_card_image_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_stories/product_detail_full_card_stories_bloc.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class ProductDetailFullCardStories extends StatefulWidget {
  final bool isFullView;
  final Product product;
  final bool isFromAddProduct;
  final ProductDetailFullCardBloc productDetailFullCardBloc;

  const ProductDetailFullCardStories({super.key, this.isFullView = true, required this.product,this.isFromAddProduct = false, required this.productDetailFullCardBloc});

  @override
  State<ProductDetailFullCardStories> createState() => _ProductDetailFullCardStoriesState();
}

class _ProductDetailFullCardStoriesState extends State<ProductDetailFullCardStories> {
  //region Bloc
  late ProductDetailFullCardStoriesBloc productDetailFullCardStoriesBloc;
  //endregion

  //region Init
  @override
  void initState() {
    productDetailFullCardStoriesBloc = ProductDetailFullCardStoriesBloc(context);
    productDetailFullCardStoriesBloc.init();
    super.initState();
  }
  //endregion


  //region Dispose
  @override
  void dispose() {
    productDetailFullCardStoriesBloc.dispose();
    super.dispose();
  }
  //endregion



  @override
  Widget build(BuildContext context) {

    //If full view
    if(widget.isFullView){
      return fullViewImage(product: widget.product);
    }
    //If small view
    else{
      return smallImageView(product: widget.product);
    }


  }






  ///Full image view
  //region Full image view
  Widget fullViewImage({required Product product}) {
    // Calculate image width minus 30 (for a margin of 15 on each side)
    final double imageWidth = CommonMethods.calculateWebWidth(context: context) - 30;

    return Stack(
      alignment: Alignment.center,
      children: [
        CarouselSlider(
          options: CarouselOptions(
            height:imageWidth,
            autoPlay: false,
            enlargeCenterPage: false,
            viewportFraction: 1,
            autoPlayCurve: Curves.linear,
            enableInfiniteScroll: false,
            onPageChanged: (index, reason) {
              productDetailFullCardStoriesBloc.sliderCtrl.add(index);
            },
          ),
          items: product.taggedStories!.asMap().entries.map((entry) {
            final int index = entry.key;
            // final imageUrl = entry.value.;
            return Builder(
              builder: (BuildContext context) {

                return CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      // widget.productDetailFullCardBloc.goToBuyerProductImageScreen(productImage: product.prodImages!, imageIndex: index);
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: LayoutBuilder(
                            builder: (BuildContext context, BoxConstraints constraints) {
                              return Container(
                                margin: const EdgeInsets.symmetric(horizontal: 15),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(imageWidth * 0.06),
                                  child:
                                  extendedImage(
                                      entry.value.icon,
                                      customPlaceHolder: AppImages.productPlaceHolder,
                                      context,
                                      imageWidth.toInt(),
                                      imageWidth.toInt(),
                                      fit: BoxFit.cover),



                                ),
                              );
                              // return PostAndProductImageWidgets(
                              //   localOrNetworkImage: imageUrl.productImage!,
                              //   imageSize: imageWidth,
                              //   paddingNextToTheImage: 0,
                              //   isProduct: true,
                              // );
                            },
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 20,vertical: 5),
                            height: imageWidth * 0.2,
                            child: Text(entry.value.title!,
                              style: AppTextStyle.access1(textColor: AppColors.appBlack),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                            ))
                      ],
                    ));
              },
            );
          }).toList(),
        ),
        //Dots
        Visibility(
          visible: product.taggedStories!.length > 1,
          child: Positioned(
            bottom: 0,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: SizedBox(
                height: 6,
                child: StreamBuilder<int>(
                    stream: productDetailFullCardStoriesBloc.sliderCtrl.stream,
                    initialData: 0,
                    builder: (context, snapshot) {
                      return ListView.builder(
                          itemCount: product.taggedStories!.length,
                          scrollDirection: Axis.horizontal,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 5),
                              child: SvgPicture.asset(
                                AppImages.dot,
                                height: 5.29,
                                width: 5.29,
                                color: snapshot.data == index ? AppColors.darkGray : AppColors.darkStroke,
                              ),
                            );
                          });
                    }),
              ),
            ),
          ),
        )
      ],
    );


  }

  //endregion

  ///Small image view
  //region Small image view
  Widget smallImageView({required Product product}) {
    return product.prodImages!.isNotEmpty
        ? Container(
        alignment: Alignment.centerLeft,
        margin: const EdgeInsets.symmetric(vertical: 5),
        padding: const EdgeInsets.symmetric(vertical: 5),
        child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              height: 236,
              child: ListView.builder(
                  shrinkWrap: true,
                  scrollDirection: Axis.horizontal,
                  itemCount: product.prodImages!.length,
                  itemBuilder: (context, index) {
                    return CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          widget.productDetailFullCardBloc.goToBuyerProductImageScreen(productImage: product.prodImages!, imageIndex: index);

                          // postCardBloc.onTapImage(index: index, imageList: widget.postDetail.images!.map((e) => e.mediaPath!).toList());
                        },
                        // child: extendedImage(widget.postDetail.postImages![index].postImage, context, 800, 800),
                        child: PostAndProductImageWidgets(
                          localOrNetworkImage: product.prodImages![index].productImage!,
                        ));
                  }),
            )))
        : Container(
      margin: const EdgeInsets.only(left: 15),
      alignment: Alignment.centerLeft,
      child: const PostAndProductImageWidgets(
        localOrNetworkImage: "",
      ),
    );
  }

//endregion



}
