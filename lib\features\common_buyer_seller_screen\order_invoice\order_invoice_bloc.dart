import 'dart:async';
import 'dart:io';
import 'package:flutter_html_to_pdf/flutter_html_to_pdf.dart';
import 'package:flutter/services.dart' show MethodChannel, PlatformException, rootBundle;
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:swadesic/services/order_invoice_service/order_invoice_service.dart';
import 'package:swadesic/util/app_common_methods/app_download_files.dart';
import 'package:swadesic/util/app_common_methods/app_permisson_handle.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:webview_flutter/webview_flutter.dart';

enum OrderInvoiceState { Loading, Success, Failed }

class OrderInvoiceBloc{

  //region Common variable
  BuildContext context;
  final String orderNumber;
  final doc = pw.Document();
  late WebViewController webViewController;

  File? pdfPath;
  //endregion

  String myHtmlData = '''
  @@@
  <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Invoice</title>\n    <style>\n        @page {\n            size: A4;\n            margin: 0;\n        }\n        body {\n            font-family: Arial, sans-serif;\n            margin: 0;\n            padding: 20mm;\n            color: #333;\n            box-sizing: border-box;\n            width: 210mm;\n            min-height: 297mm;\n        }\n        .page-break {\n            page-break-after: always;\n        }\n        .header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            margin-bottom: 20px;\n        }\n        .logo {\n            width: 150px;\n            height: auto;\n        }\n        .invoice-title {\n            font-size: 18px;\n            font-weight: bold;\n            text-align: right;\n        }\n        .info-section {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 20px;\n        }\n        .info-column {\n            width: 48%;\n        }\n        .info-item {\n            margin-bottom: 5px;\n        }\n        .bold {\n            font-weight: bold;\n        }\n        .address {\n            white-space: pre-line;\n        }\n        table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 20px;\n        }\n        th, td {\n            border: 1px solid #ddd;\n            padding: 8px;\n            text-align: left;\n        }\n        th {\n            background-color: #f2f2f2;\n        }\n        .totals {\n            width: 100%;\n            border-collapse: collapse;\n        }\n        .totals td {\n            border: 1px solid #ddd;\n            padding: 8px;\n        }\n        .totals .label {\n            text-align: left;\n        }\n        .totals .value {\n            text-align: right;\n        }\n        .signature {\n            margin-top: 20px;\n            text-align: right;\n        }\n        .signature-box {\n            border: 1px solid #333;\n            width: 200px;\n            height: 60px;\n            margin-left: auto;\n            margin-bottom: 5px;\n            position: relative;\n        }\n        .signature-image {\n            width: 100%;\n            height: 100%;\n            object-fit: contain;\n        }\n        .footer {\n            margin-top: 20px;\n            font-size: 12px;\n        }\n        @media print {\n            body {\n                width: 210mm;\n                height: 297mm;\n            }\n        }\n    </style>\n</head>\n<body>\n\n\n    <div class=\"header\">\n        <img src=\"https://github.com/Socially-X/public_assets/blob/MASTER/swadesic_logo_and_name.png?raw=true\" alt=\"Swadesic Logo\" class=\"logo\">\n        <div class=\"invoice-title\">\n            Invoice\/ Bill of Supply\/ Cash Receipt<br>\n            (original for recipient)\n        </div>\n    </div>\n\n    <div class=\"info-section\">\n        <div class=\"info-column\">\n            <div class=\"info-item\">Storename on Swadesic: radhe_sweet</div>\n            <div class=\"info-item bold\">Supplied By:</div>\n            <div class=\"info-item\">Radhe sweet</div>\n            <div class=\"info-item address\">New delhi kh no 1627, New delhi, Assam, 110068</div>\n            <div class=\"info-item bold\" style=\"margin-top: 10px;\">Sold By:</div>\n            <div class=\"info-item\">Radhe sweet</div>\n            <div class=\"info-item address\">New delhi kh no 1627, New delhi, Assam, 110068</div>\n            <div class=\"info-item\">PAN No: **********</div>\n            <div class=\"info-item\">GST Registration No: 22HHHHH0000A1Z5</div>\n        </div>\n        <div class=\"info-column\">\n            <div class=\"info-item bold\">Billing Address:</div>\n            <div class=\"info-item\">sushant_404</div>\n            <div class=\"info-item address\">House no : 55, New delhi, Delhi, 804401</div>\n            <div class=\"info-item bold\" style=\"margin-top: 10px;\">Shipping Address:</div>\n            <div class=\"info-item\">sushant_404</div>\n            <div class=\"info-item address\">House no : 55, New delhi, Delhi, 804401</div>\n            <div class=\"info-item\">+918076219463</div>\n        </div>\n    </div>\n\n    <div class=\"info-section\">\n        <div class=\"info-column\">\n            <div class=\"info-item\">Order No: O2409061650190014</div>\n            <div class=\"info-item\">Order Date: 06/09/2024</div>\n        </div>\n        <div class=\"info-column\">\n            <div class=\"info-item\">Invoice No: INV-190014</div>\n            <div class=\"info-item\">Invoice Details: Invoice for order O2409061650190014</div>\n            <div class=\"info-item\">Invoice Date: 06/09/2024</div>\n        </div>\n    </div>\n\n    <table>\n        <thead>\n            <tr>\n                <th>SI.no</th>\n                <th>Product & Description</th>\n                <th>Unit Price</th>\n                <th>Quantity</th>\n                <th>Total Price</th>\n            </tr>\n        </thead>\n        <tbody>\n            \n            <tr>\n                <td>1</td>\n                <td>Product zero Anker SoundCore 2 blk (Black)</td>\n                <td>800</td>\n                <td>1</td>\n                <td>800</td>\n            </tr>\n            \n        </tbody>\n    </table>\n\n    <table class=\"totals\">\n        <tr>\n            <td class=\"label\">Subtotal (Inclusive of Taxes)</td>\n            <td class=\"value\">800</td>\n        </tr>\n        <tr>\n            <td class=\"label\">Shipping Fee</td>\n            <td class=\"value\">30</td>\n        </tr>\n        <tr>\n            <td class=\"label\">Refund Handling Fee</td>\n            <td class=\"value\">60</td>\n        </tr>\n        <tr>\n            <td class=\"label bold\">Total Price</td>\n            <td class=\"value bold\">830</td>\n        </tr>\n        <tr>\n            <td colspan=\"2\" class=\"label\">Amount in Words: Eight Hundred and Thirty Only</td>\n        </tr>\n    </table>\n\n    <div class=\"signature\">\n        <div class=\"bold\">For Radhe sweet</div>\n        <div class=\"signature-box\">\n            <img src=\"http://**************:8000/media/stores/S1719604055197/store_signatures/.520032signature.png\" alt=\"Authorized Signature\" class=\"signature-image\">\n\n            <!-- <img src=\"data:image/png;base64,http://**************:8000/media/stores/S1719604055197/store_signatures/.520032signature.png\" alt=\"Authorized Signature\" class=\"signature-image\"> -->\n        </div>\n        <div>Authorized signatory</div>\n    </div>\n\n    <div class=\"footer\">\n        If you have any questions concerning this invoice, , +918075461312, <EMAIL><br><br>\n        <strong>THANK YOU FOR SUPPORTING A SWADESHI SMALL BUSINESS! JAI HIND!! </strong>\n\n    </div>\n</body>\n</html>
  @@@
  ''';



  //region Controller
  final buyerInvoiceCtrl  =  StreamController<OrderInvoiceState>.broadcast();

  //endregion



  // region | Constructor |
  OrderInvoiceBloc(this.context,this.orderNumber);
// endregion



//region Init
void init(){

  createPdfFromHtml();
}
//endregion



  //region Create PDF from HTML
  Future<void> createPdfFromHtml() async {
  try{
    //Loading
    buyerInvoiceCtrl.sink.add(OrderInvoiceState.Loading);


    var htmlData = await OrderInvoiceService().getOrderInvoice(orderNumber: orderNumber);


    // Read HTML content from file
    // final htmlContent = await rootBundle.loadString('assets/invoice.html');
    final htmlContent = htmlData.replaceAll('@@@', '').replaceAll('\\n', '');

    final appDocDir = await getApplicationDocumentsDirectory();
    final appDocPath = appDocDir.path;
    final fileName = 'Swadesic-invoice-$orderNumber';

    // Generate PDF file from HTML content
    final file = await FlutterHtmlToPdf.convertFromHtmlContent(
      htmlContent,
      appDocPath,
      fileName,
    );

    //Add pdf path to variable
    pdfPath = file;
    //Success
    buyerInvoiceCtrl.sink.add(OrderInvoiceState.Success);
  }
  catch(e){
    //Failed
    buyerInvoiceCtrl.sink.add(OrderInvoiceState.Failed);
  }
  }
  //endregion



//region Download invoice to local download folder

void downloadInvoiceToDownloadFolder()async{
  bool isGranted = await PermissionManager().requestPermissions();

  if (isGranted) {
    // Proceed to save the file in the download folder
    await AppDownloadFiles().saveFileToDownloadFolder(filePath: pdfPath!.path, context: context, message: "Invoice successfully saved in your 'Downloads' folder.");
  } else {
    //print('Permissions not granted. Cannot save the file.');
  }
}


//endregion






}