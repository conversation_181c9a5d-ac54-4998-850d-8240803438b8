import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_image/product_detail_full_card_image_bloc.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class ProductDetailFullCardImage extends StatefulWidget {
  final bool isFullView;
  final Product product;
  final bool isFromAddProduct;
  final ProductDetailFullCardBloc productDetailFullCardBloc;

  const ProductDetailFullCardImage(
      {super.key,
      this.isFullView = true,
      required this.product,
      this.isFromAddProduct = false,
      required this.productDetailFullCardBloc});

  @override
  State<ProductDetailFullCardImage> createState() =>
      _ProductDetailFullCardImageState();
}

class _ProductDetailFullCardImageState
    extends State<ProductDetailFullCardImage> {
  //region Bloc
  late ProductDetailFullCardImageBloc productDetailFullCardImageBloc;
  //endregion

  //region Init
  @override
  void initState() {
    productDetailFullCardImageBloc = ProductDetailFullCardImageBloc(context);
    productDetailFullCardImageBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    productDetailFullCardImageBloc.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    //If full view
    if (widget.isFullView) {
      return fullViewImage(product: widget.product);
    }
    //If small view
    else {
      return smallImageView(product: widget.product);
    }
  }

  ///Full image view
  //region Full image view
  Widget fullViewImage({required Product product}) {
    // Calculate image width minus 30 (for a margin of 15 on each side)
    final double imageWidth =
        CommonMethods.calculateWebWidth(context: context) - 30;

    //If local
    if (widget.isFromAddProduct) {
      // Check for web platform first
      if (kIsWeb) {
        // For web platform
        if (AppConstants.webProductImages.isEmpty) {
          return PostAndProductImageWidgets(
            localOrNetworkImage: "xyz.png",
            imageSize: imageWidth,
            paddingNextToTheImage: 0,
          );
        }

        return Stack(
          alignment: Alignment.center,
          children: [
            CarouselSlider(
              options: CarouselOptions(
                height: imageWidth,
                autoPlay: false,
                enlargeCenterPage: false,
                viewportFraction: 1,
                autoPlayCurve: Curves.linear,
                enableInfiniteScroll: false,
                onPageChanged: (index, reason) {
                  widget.productDetailFullCardBloc.sliderCtrl.add(index);
                },
              ),
              items: AppConstants.webProductImages.asMap().entries.map((entry) {
                final int imageIndex = entry.key;
                return Builder(
                  builder: (BuildContext context) {
                    return CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          // No action needed for preview
                        },
                        child: LayoutBuilder(
                          builder: (BuildContext context,
                              BoxConstraints constraints) {
                            return Container(
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 15),
                              child: ClipRRect(
                                  borderRadius:
                                      BorderRadius.circular(imageWidth * 0.06),
                                  child: Image.memory(
                                    entry.value['bytes'],
                                    height: imageWidth,
                                    width: imageWidth,
                                    fit: BoxFit.cover,
                                  )),
                            );
                          },
                        ));
                  },
                );
              }).toList(),
            ),
            // Dots for web images
            Visibility(
              visible: AppConstants.webProductImages.length > 1,
              child: Positioned(
                bottom: 0,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: SizedBox(
                    height: 6,
                    child: StreamBuilder<int>(
                        stream:
                            productDetailFullCardImageBloc.sliderCtrl.stream,
                        initialData: 0,
                        builder: (context, snapshot) {
                          return ListView.builder(
                              itemCount: AppConstants.webProductImages.length,
                              scrollDirection: Axis.horizontal,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 5),
                                  child: SvgPicture.asset(
                                    AppImages.dot,
                                    height: 5.29,
                                    width: 5.29,
                                    color: snapshot.data == index
                                        ? AppColors.darkGray
                                        : AppColors.darkStroke,
                                  ),
                                );
                              });
                        }),
                  ),
                ),
              ),
            )
          ],
        );
      }
      // For mobile platform
      else {
        //If there is no image
        if (AppConstants.multipleSelectedImage.isEmpty) {
          return PostAndProductImageWidgets(
            localOrNetworkImage: "xyz.png",
            imageSize: imageWidth,
            paddingNextToTheImage: 0,
          );
        }

        return Stack(
          alignment: Alignment.center,
          children: [
            CarouselSlider(
              options: CarouselOptions(
                height: imageWidth,
                autoPlay: false,
                enlargeCenterPage: false,
                viewportFraction: 1,
                autoPlayCurve: Curves.linear,
                enableInfiniteScroll: false,
                onPageChanged: (index, reason) {
                  widget.productDetailFullCardBloc.sliderCtrl.add(index);
                },
              ),
              items: AppConstants.multipleSelectedImage
                  .asMap()
                  .entries
                  .map((entry) {
                final int imageIndex = entry.key;
                return Builder(
                  builder: (BuildContext context) {
                    return CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          // No action needed for preview
                        },
                        child: LayoutBuilder(
                          builder: (BuildContext context,
                              BoxConstraints constraints) {
                            return Container(
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 15),
                              child: ClipRRect(
                                  borderRadius:
                                      BorderRadius.circular(imageWidth * 0.06),
                                  child: Image.file(
                                    File(entry.value.path),
                                    height: imageWidth,
                                    width: imageWidth,
                                    fit: BoxFit.cover,
                                  )),
                            );
                          },
                        ));
                  },
                );
              }).toList(),
            ),
            //Dots
            Visibility(
              visible: AppConstants.multipleSelectedImage.length > 1,
              child: Positioned(
                bottom: 0,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: SizedBox(
                    height: 6,
                    child: StreamBuilder<int>(
                        stream:
                            productDetailFullCardImageBloc.sliderCtrl.stream,
                        initialData: 0,
                        builder: (context, snapshot) {
                          return ListView.builder(
                              itemCount:
                                  AppConstants.multipleSelectedImage.length,
                              scrollDirection: Axis.horizontal,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 5),
                                  child: SvgPicture.asset(
                                    AppImages.dot,
                                    height: 5.29,
                                    width: 5.29,
                                    color: snapshot.data == index
                                        ? AppColors.darkGray
                                        : AppColors.darkStroke,
                                  ),
                                );
                              });
                        }),
                  ),
                ),
              ),
            )
          ],
        );
      }
    }

    ///If not from add product
    if (!widget.isFromAddProduct) {
      //If there is no image
      if (widget.product.prodImages!.isEmpty) {
        return PostAndProductImageWidgets(
          localOrNetworkImage: "xyz.png",
          imageSize: imageWidth,
          paddingNextToTheImage: 0,
        );
      }

      return Stack(
        alignment: Alignment.center,
        children: [
          CarouselSlider(
            options: CarouselOptions(
              height: imageWidth,
              autoPlay: false,
              enlargeCenterPage: false,
              viewportFraction: 1,
              autoPlayCurve: Curves.linear,
              enableInfiniteScroll: false,
              onPageChanged: (index, reason) {
                productDetailFullCardImageBloc.sliderCtrl.add(index);
              },
            ),
            items: product.prodImages!.asMap().entries.map((entry) {
              final int index = entry.key;
              final imageUrl = entry.value;
              return Builder(
                builder: (BuildContext context) {
                  return CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        widget.productDetailFullCardBloc
                            .goToBuyerProductImageScreen(
                                productImage: product.prodImages!,
                                imageIndex: index);
                      },
                      child: LayoutBuilder(
                        builder:
                            (BuildContext context, BoxConstraints constraints) {
                          return Container(
                            margin: const EdgeInsets.symmetric(horizontal: 15),
                            child: ClipRRect(
                              borderRadius:
                                  BorderRadius.circular(imageWidth * 0.06),
                              child: extendedImage(
                                  imageUrl.productImage!,
                                  customPlaceHolder:
                                      AppImages.productPlaceHolder,
                                  context,
                                  imageWidth.toInt(),
                                  imageWidth.toInt(),
                                  fit: BoxFit.cover),
                            ),
                          );
                        },
                      ));
                },
              );
            }).toList(),
          ),
          //Dots
          Visibility(
            visible: product.prodImages!.length > 1,
            child: Positioned(
              bottom: 0,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 10),
                child: SizedBox(
                  height: 6,
                  child: StreamBuilder<int>(
                      stream: productDetailFullCardImageBloc.sliderCtrl.stream,
                      initialData: 0,
                      builder: (context, snapshot) {
                        return ListView.builder(
                            itemCount: product.prodImages!.length,
                            scrollDirection: Axis.horizontal,
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 5),
                                child: SvgPicture.asset(
                                  AppImages.dot,
                                  height: 5.29,
                                  width: 5.29,
                                  color: snapshot.data == index
                                      ? AppColors.darkGray
                                      : AppColors.darkStroke,
                                ),
                              );
                            });
                      }),
                ),
              ),
            ),
          )
        ],
      );
    }

    return const SizedBox();
  }
  //endregion

  ///Small image view
  //region Small image view
  Widget smallImageView({required Product product}) {
    if (product.prodImages == null || product.prodImages!.isEmpty) {
      return const SizedBox(); // Return empty widget if no images
    }

    return Container(
        alignment: Alignment.centerLeft,
        margin: const EdgeInsets.symmetric(vertical: 5),
        padding: const EdgeInsets.symmetric(vertical: 5),
        child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              height: 236,
              child: ListView.builder(
                  shrinkWrap: true,
                  scrollDirection: Axis.horizontal,
                  itemCount: product.prodImages!.length,
                  itemBuilder: (context, index) {
                    final productImage =
                        product.prodImages![index].productImage;
                    if (productImage == null) {
                      return const SizedBox(); // Skip if image path is null
                    }
                    return CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          widget.productDetailFullCardBloc
                              .goToBuyerProductImageScreen(
                                  productImage: product.prodImages!,
                                  imageIndex: index);
                        },
                        child: PostAndProductImageWidgets(
                          localOrNetworkImage: productImage,
                        ));
                  }),
            )));
  }
  //endregion
}
