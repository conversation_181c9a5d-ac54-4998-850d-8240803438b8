import 'package:flutter/material.dart';
import 'package:swadesic/features/widgets/level_badge/level_badge.dart';
import 'package:swadesic/features/widgets/custom_badge/custom_badge.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

enum CustomImageContainerType { store, user, product, post }

class CustomImageContainer extends StatelessWidget {
  final double width;
  final double height;
  final String? imageUrl;
  final CustomImageContainerType imageType;
  final bool showShadow;

  // Level badge properties
  final bool showLevelBadge;
  final String? level;
  final double? badgeWidth;
  final double? badgeHeight;
  final double? userBadgeBorderWidth;
  final double? userBadgeFontSize;
  final double? storeBadgeBorderWidth;
  final double? storeBadgeFontSize;
  final VoidCallback? onTapBadge;

  // Custom badge properties
  final bool showCustomBadge;
  final String? customBadgeSvgPath;

  const CustomImageContainer({
    super.key,
    required this.width,
    required this.height,
    required this.imageUrl,
    this.imageType = CustomImageContainerType.user,
    this.showShadow = false,
    this.showLevelBadge = false,
    this.level,
    this.badgeWidth,
    this.badgeHeight,
    this.userBadgeBorderWidth,
    this.userBadgeFontSize,
    this.storeBadgeBorderWidth,
    this.storeBadgeFontSize,
    this.onTapBadge,
    this.showCustomBadge = false,
    this.customBadgeSvgPath,
  }) : assert(
          !(showLevelBadge && showCustomBadge),
          'Only one badge type can be shown at a time: showLevelBadge or showCustomBadge',
        );

  //region Get placeholder
  String getPlaceholder() {
    switch (imageType) {
      case CustomImageContainerType.store:
        return AppImages.storePlaceHolder;
      case CustomImageContainerType.user:
        return AppImages.userPlaceHolder;
      case CustomImageContainerType.product:
        return AppImages.productPlaceHolder;
      case CustomImageContainerType.post:
        return AppImages.productPlaceHolder;
      default:
        return AppImages.productPlaceHolder;
    }
  }
  //endregion

  Color getBorderColor() {
    // Adjust the border color based on your preferences
    return Colors.black;
  }

  @override
  Widget build(BuildContext context) {
    final borderRadius =
        CommonMethods().getBorderRadius(height: height, imageType: imageType);

    Widget imageContainer = Container(
      height: height,
      width: width,
      decoration: showShadow
          ? BoxDecoration(
              color: AppColors.appWhite,
              borderRadius: BorderRadius.circular(borderRadius),
              boxShadow: AppColors.appShadow,
            )
          : null,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: extendedImage(imageUrl, context, height.toInt(), height.toInt(),
            customPlaceHolder: getPlaceholder(),
            fit: BoxFit.cover,
            imageHeight: height,
            imageWidth: width),
      ),
    );

    // If no badge is needed, return the image container directly
    if (!showLevelBadge && !showCustomBadge) {
      return imageContainer;
    }

    // If level badge is requested but level is null, return image container
    if (showLevelBadge && level == null) {
      return imageContainer;
    }

    // If custom badge is requested but SVG path is null, return image container
    if (showCustomBadge && customBadgeSvgPath == null) {
      return imageContainer;
    }

    Widget badgeWidget;
    double bottomPosition, rightPosition;

    if (showCustomBadge && customBadgeSvgPath != null) {
      // Calculate default badge dimensions for positioning
      double defaultBadgeSize = height * 0.35; // 35% of container height
      double finalBadgeWidth = badgeWidth ?? defaultBadgeSize;
      double finalBadgeHeight = badgeHeight ?? defaultBadgeSize;

      // Determine custom badge type based on image type
      CustomBadgeType customBadgeType =
          (imageType == CustomImageContainerType.store)
              ? CustomBadgeType.store
              : CustomBadgeType.user;

      // Select appropriate border width based on badge type (exactly like LevelBadge)
      double? selectedBorderWidth;
      if (customBadgeType == CustomBadgeType.user) {
        selectedBorderWidth = userBadgeBorderWidth;
      } else {
        selectedBorderWidth = storeBadgeBorderWidth;
      }

      // Create custom badge widget
      badgeWidget = CustomBadge.createCustomBadge(
        svgAssetPath: customBadgeSvgPath!,
        badgeType: customBadgeType,
        width: finalBadgeWidth,
        height: finalBadgeHeight,
        borderWidth: selectedBorderWidth,
      );

      // Calculate positioning based on badge type (exactly like LevelBadge)
      if (customBadgeType == CustomBadgeType.user) {
        // For user badges, position them more towards the center/edge
        bottomPosition =
            -finalBadgeHeight * 0.15; // Less overlap for user badges
        rightPosition = -finalBadgeWidth * 0.15; // Less overlap for user badges
      } else {
        // For store badges, keep the current positioning
        bottomPosition =
            -finalBadgeHeight * 0.3; // More overlap for store badges
        rightPosition = -finalBadgeWidth * 0.3; // More overlap for store badges
      }
    } else {
      // Use level badge
      // Determine badge type based on image type
      LevelBadgeType badgeType = (imageType == CustomImageContainerType.store)
          ? LevelBadgeType.store
          : LevelBadgeType.user;

      // Calculate default badge dimensions if not provided
      double defaultBadgeSize = height * 0.35; // 35% of container height
      double finalBadgeWidth = badgeWidth ?? defaultBadgeSize;
      double finalBadgeHeight = badgeHeight ?? defaultBadgeSize;

      // Calculate positioning based on badge type
      if (badgeType == LevelBadgeType.user) {
        // For user badges, position them more towards the center/edge
        bottomPosition =
            -finalBadgeHeight * 0.15; // Less overlap for user badges
        rightPosition = -finalBadgeWidth * 0.15; // Less overlap for user badges
      } else {
        // For store badges, keep the current positioning
        bottomPosition =
            -finalBadgeHeight * 0.3; // More overlap for store badges
        rightPosition = -finalBadgeWidth * 0.3; // More overlap for store badges
      }

      // Select appropriate border width and font size based on badge type
      double? selectedBorderWidth;
      double? selectedFontSize;

      if (badgeType == LevelBadgeType.user) {
        selectedBorderWidth = userBadgeBorderWidth;
        selectedFontSize = userBadgeFontSize;
      } else {
        selectedBorderWidth = storeBadgeBorderWidth;
        selectedFontSize = storeBadgeFontSize;
      }

      // Create the badge widget
      badgeWidget = LevelBadge.createLevelBadge(
        level: level!,
        badgeType: badgeType,
        width: finalBadgeWidth,
        height: finalBadgeHeight,
        borderWidth: selectedBorderWidth,
        fontSize: selectedFontSize,
      );
    }

    // Wrap badge in GestureDetector if onTapBadge is provided
    if (onTapBadge != null) {
      badgeWidget = GestureDetector(
        onTap: onTapBadge,
        child: badgeWidget,
      );
    }

    return Stack(
      clipBehavior: Clip.none, // Allow badge to extend outside container
      children: [
        imageContainer,
        Positioned(
          bottom: bottomPosition,
          right: rightPosition,
          child: badgeWidget,
        ),
      ],
    );
  }
}
