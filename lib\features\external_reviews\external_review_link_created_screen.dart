import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:swadesic/features/external_reviews/external_review_link_created_bloc.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';

class ExternalReviewLinkCreatedScreen extends StatefulWidget {
  final String token;
  final String expiresAt;
  final String userIdentifier;
  final String productReference;
  final String productName;
  final String? productImage;
  final String storeHandle;

  const ExternalReviewLinkCreatedScreen({
    Key? key,
    required this.token,
    required this.expiresAt,
    required this.userIdentifier,
    required this.productReference,
    required this.productName,
    this.productImage,
    required this.storeHandle,
  }) : super(key: key);

  @override
  ExternalReviewLinkCreatedScreenState createState() =>
      ExternalReviewLinkCreatedScreenState();
}

class ExternalReviewLinkCreatedScreenState
    extends State<ExternalReviewLinkCreatedScreen> {
  late ExternalReviewLinkCreatedBloc bloc;

  @override
  void initState() {
    super.initState();
    bloc = ExternalReviewLinkCreatedBloc(
      context,
      widget.token,
      widget.expiresAt,
      widget.userIdentifier,
      widget.productReference,
      widget.storeHandle,
    );
  }

  @override
  void dispose() {
    // Schedule the flag reset for the next frame to avoid accessing deactivated widgets
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AppConstants.isSignInScreenOpenedForStatisUser = false;
    });
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.appWhite,
        elevation: 0,
        leading: IconButton(
          icon: SvgPicture.asset(AppImages.backButton),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'External Review Link Created',
          style: AppTextStyle.pageHeading2(textColor: AppColors.appBlack),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 5),
            _buildQRCodeSection(),
            const SizedBox(height: 24),
            _buildLinkCreatedCard(),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildLinkCreatedCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.lightGray.withOpacity(0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    bloc.reviewLink,
                    style: AppTextStyle.smallText(
                      textColor: AppColors.brandBlack,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 2),
                GestureDetector(
                  onTap: bloc.onTapCopyLink,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.brandBlack,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.copy,
                      color: AppColors.appWhite,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          _buildShareLinkButton(),
          const SizedBox(height: 16),
          Text(
            bloc.getFormattedExpiryDate(),
            style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildShareLinkButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: AppColors.appWhite,
            builder: (context) => ShareWithImageScreen(
              url: bloc.reviewLink,
              imageType: CustomImageContainerType.product,
              entityType: EntityType.PRODUCT,
              message: 'Please review this product:${widget.productName}  ',
              productName: widget.productName,
              imageLink: widget.productImage,
              storeIconUrl: null, // We don't have store icon in this context
              objectReference: widget.productReference,
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandBlack,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          'Share the link',
          style: AppTextStyle.heading1Medium(
            textColor: AppColors.appWhite,
          ),
        ),
      ),
    );
  }

  Widget _buildQRCodeSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'QR Code for External Review',
            style: AppTextStyle.contentHeading0(
              textColor: AppColors.appBlack,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            width: 200, // Fixed width for the QR code container
            child: Stack(
              alignment: Alignment.center,
              children: [
                // QR Code with embedded square
                SizedBox(
                  width: 180, // Reduced QR code size
                  height: 180,
                  child: PrettyQrView.data(
                    data: bloc.reviewLink,
                    errorCorrectLevel: QrErrorCorrectLevel.H,
                    decoration: const PrettyQrDecoration(
                      shape: PrettyQrSmoothSymbol(
                        color: AppColors.appBlack,
                        roundFactor: 1,
                      ),
                      image: PrettyQrDecorationImage(
                        image:
                            AssetImage('assets/common_images/white_square.png'),
                        position: PrettyQrDecorationImagePosition.embedded,
                        scale: 0.26,
                      ),
                    ),
                  ),
                ),
                // App icon in the center
                Positioned(
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade200, width: 2),
                    ),
                    child: widget.productImage != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(6),
                            child: CustomImageContainer(
                              width: 36,
                              height: 36,
                              imageUrl: widget.productImage!,
                              imageType: CustomImageContainerType.product,
                              showShadow: false,
                            ),
                          )
                        : Image.asset(
                            'assets/common_images/app_icon_png.png',
                            width: 36,
                            height: 36,
                            fit: BoxFit.cover,
                          ),
                  ),
                ),
              ],
            ),
          ),
          // const SizedBox(height: 16),
          // Text(
          //   'Scan to leave a review for ${widget.productName}',
          //   textAlign: TextAlign.center,
          //   style: AppTextStyle.smallText(
          //     textColor: AppColors.writingBlack1,
          //   ),
          // ),
        ],
      ),
    );
  }
}
