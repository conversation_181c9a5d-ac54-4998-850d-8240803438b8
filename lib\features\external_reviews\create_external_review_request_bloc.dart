import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/external_reviews/external_review_link_created_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/external_review_service/external_review_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum ExternalReviewRequestState { initial, loading, success, failed }

class CreateExternalReviewRequestBloc {
  // region Common Variables
  BuildContext context;
  Product product;
  String? productImage;

  // User identifier (username, phone, or email)
  TextEditingController userIdentifierController = TextEditingController();

  // Selected identifier type
  String selectedIdentifierType = 'username'; // Default to username

  // External review service
  final ExternalReviewService _externalReviewService = ExternalReviewService();

  // State controller
  final stateCtrl = StreamController<ExternalReviewRequestState>.broadcast();

  // Response data
  Map<String, dynamic>? responseData;

  // endregion

  // region | Constructor |
  CreateExternalReviewRequestBloc(
      this.context, this.product, this.productImage);
  // endregion

  // region Init
  void init() {
    // Initialize any required data
  }
  // endregion

  // region On tap create link
  Future<void> onTapCreateLink() async {
    try {
      // Validate input
      if (userIdentifierController.text.trim().isEmpty) {
        CommonMethods.toastMessage("Please enter a valid identifier", context);
        return;
      }

      // Set loading state
      stateCtrl.sink.add(ExternalReviewRequestState.loading);

      // Format user identifier based on selected type
      String userIdentifier = userIdentifierController.text.trim();

      // Check if a review already exists for this user and product
      bool reviewExists =
          await _externalReviewService.checkExternalReviewExists(
        productReference: product.productReference!,
        userIdentifier: userIdentifier,
      );

      if (reviewExists) {
        // Set failed state
        stateCtrl.sink.add(ExternalReviewRequestState.failed);
        if (context.mounted) {
          CommonMethods.toastMessage(
              "This user has already submitted a review for this product. Each user can only submit one review per product.",
              context);
        }
        return;
      }

      // Create external review request
      responseData = await _externalReviewService.createExternalReviewRequest(
        productReference: product.productReference!,
        userIdentifier: userIdentifier,
      );

      // Set success state
      stateCtrl.sink.add(ExternalReviewRequestState.success);

      // Navigate to link created screen
      _navigateToLinkCreatedScreen();
    } on ApiErrorResponseMessage catch (error) {
      // Set failed state
      stateCtrl.sink.add(ExternalReviewRequestState.failed);
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
    } catch (error) {
      // Set failed state
      stateCtrl.sink.add(ExternalReviewRequestState.failed);
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context)
          : null;
    }
  }
  // endregion

  // region Navigate to link created screen
  void _navigateToLinkCreatedScreen() {
    if (responseData != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ExternalReviewLinkCreatedScreen(
            token: responseData!['token'],
            expiresAt: responseData!['expires_at'],
            userIdentifier: responseData!['user_identifier'],
            productReference: product.productReference!,
            productName: product.productName!,
            productImage: productImage,
            storeHandle: product.storehandle!,
          ),
        ),
      );
    }
  }
  // endregion

  // region On tap know more
  void onTapKnowMore() {
    // Show information about external reviews
    CommonMethods.appDialogBox(
      context: context,
      widget: AlertDialog(
        title: const Text('About External Reviews'),
        content: const Text(
            'External reviews allow customers who purchased your product outside Swadesic to leave reviews. '
            'Create a review request link and share it with your customers to collect their feedback.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
  // endregion

  // region Dispose
  void dispose() {
    stateCtrl.close();
    userIdentifierController.dispose();
  }
  // endregion
}
