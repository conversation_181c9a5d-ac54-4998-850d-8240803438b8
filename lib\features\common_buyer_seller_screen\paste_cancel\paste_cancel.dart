import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

class PasteCancel extends StatelessWidget {
  final dynamic onTapPaste;
  const PasteCancel({Key? key, this.onTapPaste}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
            margin: const EdgeInsets.symmetric(vertical: 20),
            child: AppCommonWidgets.emptyResponseText(
                emptyMessage: AppStrings.pasteInviteCode)),
        SizedBox(
          height: 45,
          child: Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                    onTapPaste();
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(80),
                      color: AppColors.brandBlack,
                    ),
                    child: Center(
                        child: appText("Paste", color: AppColors.appWhite)),
                  ),
                ),
              ),
              horizontalSizedBox(20),
              Expanded(
                child: InkWell(
                  onTap: () async {
                    Navigator.pop(context);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(80),
                      color: AppColors.textFieldFill1,
                    ),
                    child: Center(
                        child:
                            appText("Cancel", color: AppColors.writingColor2)),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
