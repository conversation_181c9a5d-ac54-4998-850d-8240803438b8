import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/services/deep_link/deep_link.dart';
import 'package:swadesic/model/deep_link_response/deep_link_response.dart';

class ExternalReviewLinkCreatedBloc {
  // region Common Variables
  BuildContext context;
  String token;
  String expiresAt;
  String userReference;
  String productReference;
  String storeHandle;

  // Generated link
  String reviewLink = '';

  late DeepLinkServices deepLinkServices;
  late DeepLinkCreateResponse deepLinkCreateResponse;

  // endregion

  // region | Constructor |
  ExternalReviewLinkCreatedBloc(this.context, this.token, this.expiresAt,
      this.userReference, this.productReference, this.storeHandle) {
    // Set a flag to prevent automatic redirection for static users
    AppConstants.isSignInScreenOpenedForStatisUser = true;
    deepLinkServices = DeepLinkServices();

    // Generate the review link immediately with a safe implementation
    _generateReviewLinkSafely();
  }
  // endregion

  // region Generate review link safely
  void _generateReviewLinkSafely() {
    // Create a simple external review link without using Provider
    // This avoids the issue with accessing Provider during widget disposal
    String storehandledata = "$storeHandle/e-review-request/";
    String data = "?t=$token&pr=$productReference&ur=$userReference";
    var encodedData = CommonMethods.encodeBase32(data);
    reviewLink = "${AppConstants.domainName}$storehandledata$encodedData";

    // Skip Firebase deep link creation to avoid potential issues
    // We'll use only the base32 encoded link
  }

  // We've removed the Firebase deep link creation to avoid potential issues
  // endregion

  // region On tap share link
  void onTapShareLink() {
    // Share the link using platform's share functionality
    CommonMethods.share(reviewLink);
  }
  // endregion

  // region On tap copy link
  void onTapCopyLink() {
    // Copy the link to clipboard
    Clipboard.setData(ClipboardData(text: reviewLink)).then((_) {
      CommonMethods.toastMessage('Link copied to clipboard', context);
    });
  }
  // endregion

  // region Get formatted expiry date
  String getFormattedExpiryDate() {
    try {
      // Parse the expiry date from the API response
      DateTime expiryDate = DateTime.parse(expiresAt);

      // Calculate days remaining
      DateTime now = DateTime.now();
      int daysRemaining = expiryDate.difference(now).inDays;

      return 'The review request link expires in $daysRemaining days.';
    } catch (e) {
      return 'The review request link expires in 7 days.';
    }
  }
  // endregion
}
