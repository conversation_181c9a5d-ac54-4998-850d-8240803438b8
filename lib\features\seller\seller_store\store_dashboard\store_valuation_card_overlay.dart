import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/faq/faq_navigation.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/level_badge/level_badge.dart';
import 'package:swadesic/model/contribution_response/contribution_response.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class StoreValuationCardOverlay extends StatefulWidget {
  final String? storeReference;
  final dynamic storeInfo;

  const StoreValuationCardOverlay({
    Key? key,
    this.storeReference,
    this.storeInfo,
  }) : super(key: key);

  @override
  State<StoreValuationCardOverlay> createState() =>
      _StoreValuationCardOverlayState();
}

class _StoreValuationCardOverlayState extends State<StoreValuationCardOverlay> {
  final GlobalKey _cardKey = GlobalKey();
  bool _isCapturingImage = false; // Flag to hide share button during capture

  // API related state
  List<ContributionItem> _storeMilestones = [];
  bool _isLoadingMilestones = true;
  final SingleStoreInfoServices _storeInfoService = SingleStoreInfoServices();

  @override
  void initState() {
    super.initState();
    _fetchStoreMilestones();
  }

  Future<void> _fetchStoreMilestones() async {
    try {
      // Use the provided store reference or fall back to current user's store
      final storeReference =
          widget.storeReference ?? AppConstants.appData.storeReference;
      if (storeReference != null && storeReference.isNotEmpty) {
        final response = await _storeInfoService.getStoreMilestones(
          storeReference: storeReference,
        );

        if (mounted) {
          setState(() {
            _storeMilestones = response.data ?? [];
            _isLoadingMilestones = false;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _isLoadingMilestones = false;
          });
        }
      }
    } catch (e) {
      debugPrint('Error fetching store milestones: $e');
      if (mounted) {
        setState(() {
          _isLoadingMilestones = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Blurred background with tap to close
        Positioned.fill(
          child: GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: BackdropFilter(
              filter: ui.ImageFilter.blur(sigmaX: 8, sigmaY: 8),
              child: Container(
                color: AppColors.appBlack.withOpacity(0.3),
              ),
            ),
          ),
        ),
        // Card content
        Center(
          child: GestureDetector(
            onTap: () {
              // Prevent tap from propagating to the background
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              child: RepaintBoundary(
                key: _cardKey,
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.appWhite,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      // Bottom shadow
                      BoxShadow(
                        color: AppColors.appBlack.withOpacity(0.2),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                      // Top shadow
                      BoxShadow(
                        color: AppColors.appBlack.withOpacity(0.1),
                        blurRadius: 15,
                        offset: const Offset(0, -5),
                      ),
                      // Left shadow
                      BoxShadow(
                        color: AppColors.appBlack.withOpacity(0.1),
                        blurRadius: 15,
                        offset: const Offset(-5, 0),
                      ),
                      // Right shadow
                      BoxShadow(
                        color: AppColors.appBlack.withOpacity(0.1),
                        blurRadius: 15,
                        offset: const Offset(5, 0),
                      ),
                    ],
                  ),
                  child: _buildCardContent(context),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCardContent(BuildContext context) {
    // Use provided store info or fall back to Consumer for current user's store
    if (widget.storeInfo != null) {
      final storeInfo = widget.storeInfo;

      // Get store valuation and level
      final storeValuation = storeInfo.storeValuation ?? 0.0;
      final storeLevel = int.tryParse(storeInfo.storeLevel ?? "1") ?? 1;

      // Level ranges mapping (dummy data)
      final Map<int, Map<String, double>> levelRanges = {
        1: {'min': 0, 'max': 100000},
        2: {'min': 100000, 'max': 500000},
        3: {'min': 500000, 'max': 1000000},
        4: {'min': 1000000, 'max': 2000000},
        5: {'min': 2000000, 'max': 5000000},
        6: {'min': 5000000, 'max': 10000000},
        7: {'min': 10000000, 'max': 20000000},
        8: {'min': 20000000, 'max': 50000000},
        9: {'min': 50000000, 'max': 50000000}, // Max level
      };

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Store profile section
          _buildStoreProfileSection(storeInfo, storeLevel),

          verticalSizedBox(20),

          // Store valuation section
          _buildStoreValuationSection(storeValuation, storeLevel, levelRanges),

          verticalSizedBox(5),

          // Store contributions section
          _buildStoreContributionsSection(),

          verticalSizedBox(20),

          // Date section
          _buildDateSection(),

          verticalSizedBox(30),

          // Bottom action row (hidden during image capture)
          if (!_isCapturingImage) _buildBottomActionRow(context, storeInfo),
        ],
      );
    }

    // Fall back to Consumer for current user's store (backward compatibility)
    return Consumer<SellerOwnStoreInfoDataModel>(
      builder: (context, storeDataModel, child) {
        final storeInfo = storeDataModel.storeInfo;

        if (storeInfo == null) {
          return const SizedBox();
        }

        // Get store valuation and level
        final storeValuation = storeInfo.storeValuation ?? 0.0;
        final storeLevel = int.tryParse(storeInfo.storeLevel ?? "1") ?? 1;

        // Level ranges mapping (dummy data)
        final Map<int, Map<String, double>> levelRanges = {
          1: {'min': 0, 'max': 100000},
          2: {'min': 100000, 'max': 500000},
          3: {'min': 500000, 'max': 1000000},
          4: {'min': 1000000, 'max': 2000000},
          5: {'min': 2000000, 'max': 5000000},
          6: {'min': 5000000, 'max': 10000000},
          7: {'min': 10000000, 'max': 20000000},
          8: {'min': 20000000, 'max': 50000000},
          9: {'min': 50000000, 'max': 50000000}, // Max level
        };

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Store profile section
            _buildStoreProfileSection(storeInfo, storeLevel),

            verticalSizedBox(20),

            // Store valuation section
            _buildStoreValuationSection(
                storeValuation, storeLevel, levelRanges),

            verticalSizedBox(5),

            // Store contributions section
            _buildStoreContributionsSection(),

            verticalSizedBox(20),

            // Date section
            _buildDateSection(),

            verticalSizedBox(30),

            // Bottom action row (hidden during image capture)
            if (!_isCapturingImage) _buildBottomActionRow(context, storeInfo),
          ],
        );
      },
    );
  }

  Widget _buildStoreProfileSection(storeInfo, int storeLevel) {
    return Column(
      children: [
        // Store image, shuffle icon, and level badge row
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Store image with circular background
            SizedBox(
              width: 90,
              height: 90,
              child: Center(
                child: CustomImageContainer(
                  width: 90,
                  height: 90,
                  imageUrl: storeInfo.icon,
                  imageType: CustomImageContainerType.store,
                ),
              ),
            ),

            horizontalSizedBox(20),

            // Shuffle icon
            SvgPicture.asset(
              AppImages.shuffleIcon,
              width: 40,
              height: 40,
              color: AppColors.appBlack,
            ),

            horizontalSizedBox(20),

            // Store level badge using the LevelBadge widget
            LevelBadge(
              level: storeLevel.toString(),
              badgeType: LevelBadgeType.store,
              width: 92,
              height: 92,
              fontSize: 64,
              borderWidth: 0,
            ),
          ],
        ),

        verticalSizedBox(16),

        // Store name
        Text(
          storeInfo.storeName ?? "Store",
          style: AppTextStyle.usernameHeading(textColor: AppColors.appBlack),
        ),

        // Store handle
        Text(
          "@${storeInfo.storehandle ?? "store"}",
          style: AppTextStyle.access0(textColor: AppColors.brandBlack),
        ),

        verticalSizedBox(12),

        // Store level description text
        Text(
          _getStoreDescriptionText(storeLevel),
          style: AppTextStyle.access0(textColor: AppColors.appBlack),
          textAlign: TextAlign.left,
        ),
      ],
    );
  }

  Widget _buildStoreValuationSection(double storeValuation, int storeLevel,
      Map<int, Map<String, double>> levelRanges) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          "Store Milestones:",
          style: AppTextStyle.access0(textColor: AppColors.appBlack),
        ),
      ],
    );
  }

  Widget _buildStoreContributionsSection() {
    // Show loading indicator while fetching data
    if (_isLoadingMilestones) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: CircularProgressIndicator(
            color: AppColors.brandBlack,
          ),
        ),
      );
    }

    // Show "Soon to be available" if no data
    if (_storeMilestones.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Text(
            "Soon to be available",
            style: AppTextStyle.smallText(textColor: AppColors.writingBlack0),
          ),
        ),
      );
    }

    // Show API data
    return Column(
      children: [
        for (int i = 0; i < _storeMilestones.length; i += 2)
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Expanded(
                  child: _buildContributionItem(
                    _storeMilestones[i].label ?? '',
                    _storeMilestones[i].value ?? '',
                  ),
                ),
                horizontalSizedBox(20),
                Expanded(
                  child: i + 1 < _storeMilestones.length
                      ? _buildContributionItem(
                          _storeMilestones[i + 1].label ?? '',
                          _storeMilestones[i + 1].value ?? '',
                        )
                      : const SizedBox(),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildContributionItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyle.smallText(textColor: AppColors.writingBlack0),
        ),
        verticalSizedBox(4),
        Text(
          value,
          style: AppTextStyle.smallText(textColor: AppColors.appBlack),
        ),
      ],
    );
  }

  Widget _buildBottomActionRow(BuildContext context, storeInfo) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Learn how to level up text
        // GestureDetector(
        //   onTap: () {
        //     FaqNavigation.navigateToFaqQuestion(
        //         context, 'general', 'general_what_is_swadeshi');
        //   },
        //   child: Text(
        //     "Learn how to level up!",
        //     style:
        //         AppTextStyle.subTitle(textColor: AppColors.brandGreen).copyWith(
        //       decoration: TextDecoration.underline,
        //       decorationColor: AppColors.brandGreen,
        //     ),
        //   ),
        // ),
        // horizontalSizedBox(30),
        // Share button
        GestureDetector(
          onTap: () {
            _shareCard(context, storeInfo);
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                AppImages.chainIcon,
                width: 16,
                height: 16,
                color: AppColors.appBlack,
              ),
              horizontalSizedBox(3),
              Text(
                "Share this card",
                style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Method to capture the card as an image
  Future<File?> _captureCardAsImage() async {
    try {
      // Get the boundary before any async operations
      final context = _cardKey.currentContext;
      if (context == null) return null;

      RenderRepaintBoundary boundary =
          context.findRenderObject() as RenderRepaintBoundary;

      // Hide share button during capture
      setState(() {
        _isCapturingImage = true;
      });

      // Wait for UI to update
      await Future.delayed(const Duration(milliseconds: 100));

      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();

      // Get application documents directory
      final directory = await getApplicationDocumentsDirectory();
      final path =
          '${directory.path}/store_valuation_card_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File(path);

      // Save the image file
      await file.writeAsBytes(pngBytes);

      // Show share button again
      setState(() {
        _isCapturingImage = false;
      });

      return file;
    } catch (e) {
      debugPrint('Error capturing card as image: $e');

      // Make sure to show share button again even if there's an error
      setState(() {
        _isCapturingImage = false;
      });

      return null;
    }
  }

  void _shareCard(BuildContext context, storeInfo) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(
          child: CircularProgressIndicator(
            color: AppColors.brandBlack,
          ),
        );
      },
    );

    // Capture the card as an image
    File? cardImage = await _captureCardAsImage();

    // Check if widget is still mounted before using context
    if (!mounted) return;

    // Close loading indicator
    Navigator.of(context).pop();

    // Create store profile URL
    String url = "${AppConstants.domainName}${storeInfo.storehandle ?? ""}";

    // Add invite code if available
    if (storeInfo.inviteCode != null && storeInfo.inviteCode!.isNotEmpty) {
      url = "$url/?ic=${storeInfo.inviteCode}";
    }

    // Close the overlay
    Navigator.of(context).pop();

    // Open share screen with store profile and the captured card image
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: url,
        imageLink: storeInfo.icon, // Use store icon for preview
        imageType: CustomImageContainerType.store,
        entityType: EntityType.STORE,
        storeName: storeInfo.storeName,
        storeIconUrl: storeInfo.icon,
        objectReference: storeInfo.storeReference,
        message: "Check out my Swadesic store valuation card!",
        attachmentImagePath:
            cardImage?.path, // Pass the captured card image path
      ),
      context: context,
    );
  }

  // Helper method to get store description text based on level
  String _getStoreDescriptionText(int storeLevel) {
    final Map<int, int> levelMinValuations = {
      1: 0,
      2: 100000,
      3: 500000,
      4: 1000000,
      5: 2000000,
      6: 6000000,
      7: 10000000,
      8: 20000000,
      9: 50000000,
    };

    if (storeLevel == 1) {
      // For level 1, show current status with valuation under next level
      final level2Min = levelMinValuations[2] ?? 100000;
      final level2MinFormatted = "${(level2Min / 100000).toStringAsFixed(0)}K";
      return "This store is currently at S$storeLevel with a Swadesic valuation under ₹$level2MinFormatted";
    } else {
      // For other levels, show earned status with valuation above current level minimum
      final currentMin = levelMinValuations[storeLevel] ?? 0;
      final currentMinFormatted =
          "${(currentMin / 100000).toStringAsFixed(0)}L";
      return "This store has earned S$storeLevel status with a valuation above ₹$currentMinFormatted on Swadesic";
    }
  }

  // Build date section
  Widget _buildDateSection() {
    final now = DateTime.now();
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];

    String getOrdinalSuffix(int day) {
      if (day >= 11 && day <= 13) {
        return 'th';
      }
      switch (day % 10) {
        case 1:
          return 'st';
        case 2:
          return 'nd';
        case 3:
          return 'rd';
        default:
          return 'th';
      }
    }

    final dateString =
        "Dated: ${months[now.month - 1]} ${now.day}${getOrdinalSuffix(now.day)}, ${now.year}";

    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        dateString,
        style: AppTextStyle.smallText(textColor: AppColors.writingBlack0),
      ),
    );
  }
}
