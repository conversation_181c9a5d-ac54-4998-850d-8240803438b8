import 'package:flutter/material.dart';
import 'package:swadesic/features/post/post_screen.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

class StoreReviewsScreen extends StatefulWidget {
  final String storeReference;
  final String storeName;

  const StoreReviewsScreen({
    Key? key,
    required this.storeReference,
    required this.storeName,
  }) : super(key: key);

  @override
  State<StoreReviewsScreen> createState() => _StoreReviewsScreenState();
}

class _StoreReviewsScreenState extends State<StoreReviewsScreen> with AutomaticKeepAliveClientMixin<StoreReviewsScreen> {
  // Keep alive
  @override
  bool get wantKeepAlive => true;

  // Controller for the PostScreen
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      appBar: AppCommonWidgets.mainAppBar(
        context: context,
        isTitleVisible: true,
        title: '${widget.storeName}\'s ${AppStrings.reviewsLowerCase}',
        isLeadingVisible: true,
        isMembershipVisible: false,
        isCartVisible: false,
      ),
      body: PostScreen(
        storeOrUserReference: widget.storeReference,
        previousScrollController: _scrollController,
        contentType: PostScreenContentType.storeReviews,
      ),
    );
  }
}
