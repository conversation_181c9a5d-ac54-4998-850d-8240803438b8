import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/services/app_link_services/page_url_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/common_widgets.dart';

class ProductFullView extends StatefulWidget {
  final Product product;
  const ProductFullView({super.key, required this.product});

  @override
  State<ProductFullView> createState() => _ProductFullViewState();
}

class _ProductFullViewState extends State<ProductFullView> {
  @override
  Widget build(BuildContext context) {
    // Set page URL for web app navigation using the same URL as sharing
    String productShareUrl = AppLinkCreateService()
        .createProductLink(productReference: widget.product.productReference!);

    // Extract path from full URL for web navigation
    String urlPath = productShareUrl;
    try {
      Uri uri = Uri.parse(productShareUrl);
      urlPath = '${uri.path}${uri.query.isNotEmpty ? '?${uri.query}' : ''}';
    } catch (e) {
      // Use full URL if parsing fails
    }

    PageUrlService.setPageUrlAfterBuild(
        urlPath, '${widget.product.productName} - ${widget.product.storeName}');

    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      resizeToAvoidBottomInset: false,
      body: body(),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: true,
      customTitleWidget: AppCommonWidgets.appBarTitleText(
        text: widget.product.storehandle!,
      ),
      // titleWidget: Text(UserProfileBloc.getUserDetailsResponse.userDetail!.userName!??"", style: TextStyle(fontFamily: AppConstants.rRegular, fontSize: 19, fontWeight: FontWeight.w700, color: AppColors.appBlack)),
      isDefaultMenuVisible: true,
      isCustomMenuVisible: false,
      isCartVisible: false,
      // customMenuButton: myMenuButton(),
    );
  }

//endregion

//region Body
  Widget body() {
    return SingleChildScrollView(
      child: ProductDetailFullCard(
        product: widget.product,
        isFullView: true,
        isFromAddProduct: false,
      ),
    );
  }
//endregion
}
